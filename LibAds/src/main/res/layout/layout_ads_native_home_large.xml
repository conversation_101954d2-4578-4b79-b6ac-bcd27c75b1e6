<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#EEE3E3"
        app:layout_constraintDimensionRatio="310:207"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_7dp"
            android:layout_marginVertical="@dimen/_6dp"
            android:gravity="center"
            android:background="@color/black"
            app:layout_constraintBottom_toTopOf="@id/ad_call_to_action"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_7dp"
            android:layout_marginBottom="@dimen/_5dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="#3EDB3A"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="296:32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="OPEN" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_2"
            android:backgroundTint="#CC646464"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:layout_width="@dimen/_42dp"
                android:layout_height="@dimen/_42dp"
                android:layout_gravity="center_vertical"
                android:layout_marginVertical="@dimen/_4dp"
                android:layout_marginStart="@dimen/_9dp"
                android:background="@color/black"
                app:cardCornerRadius="@dimen/_8dp">

                <FrameLayout
                    android:id="@+id/ad_app_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginHorizontal="@dimen/_9dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ad_headline"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/_46dp"
                    android:fontFamily="@font/font_600"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sp"
                    tools:text="Duck Adventure App" />

                <TextView
                    android:id="@+id/ad_body"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_1dp"
                    android:fontFamily="@font/font_600"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_14sp"
                    tools:text="Mô tả của quảng cáo ở đây" />

            </LinearLayout>

        </LinearLayout>

        <TextView
            android:id="@+id/txv_ads"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:background="@drawable/bg_radius_1"
            android:backgroundTint="#FFA800"
            android:fontFamily="@font/font_500"
            android:paddingHorizontal="@dimen/_4dp"
            android:paddingVertical="@dimen/_1dp"
            android:text="@string/ad_attribution"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>