<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:tag="collapsible">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/adViewHolder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/ads_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <TextView
            android:id="@+id/ad_headline"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_11dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:ellipsize="end"
            android:fontFamily="@font/font_600"
            android:maxLines="1"
            android:textColor="@color/content_text_color"
            android:textSize="@dimen/_16sp"
            app:layout_constraintBottom_toTopOf="@+id/bodyContainer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Duck Adventure App Duck Adventure App Duck Adventure App Duck Adventure App" />

        <LinearLayout
            android:id="@+id/bodyContainer"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_6dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/txv_ads"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@drawable/bg_radius_1"
                android:backgroundTint="#FFA800"
                android:fontFamily="@font/font_500"
                android:paddingHorizontal="@dimen/_2dp"
                android:text="@string/ad_attribution"
                android:textColor="@color/white" />


            <TextView
                android:id="@+id/ad_body"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/_10dp"
                android:layout_weight="1"
                android:ellipsize="end"
                android:fontFamily="@font/font_500"
                android:maxLines="1"
                android:text="Đây là mô tả của đoạn quảng Đây là mô tả của đoạn quảng Đây là mô tả của đoạn quảng"
                android:textColor="@color/content_text_color"
                android:textSize="@dimen/_12sp"
                app:layout_constraintBottom_toTopOf="@+id/ad_call_to_action"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ad_app_icon" />

        </LinearLayout>

        <TextView
            android:id="@+id/ad_call_to_action"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginBottom="@dimen/_11dp"
            android:background="@drawable/bg_radius_10"
            android:backgroundTint="@color/cta_color"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:text="OPEN"
            android:textColor="@color/cta_text_color"
            android:textSize="@dimen/_12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="342:42"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>