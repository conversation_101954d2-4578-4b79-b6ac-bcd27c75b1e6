<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#051022">

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/grey_a1"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black" />

        </FrameLayout>

    </FrameLayout>

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_55dp"
        android:background="#051022"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/_44dp"
            android:layout_height="match_parent"
            android:paddingVertical="@dimen/_15dp"
            android:paddingStart="@dimen/_20dp"
            android:src="@drawable/ic_back" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20dp"
            android:fontFamily="@font/font_700"
            android:text="@string/watch_live"
            android:textColor="@color/white"
            android:textSize="@dimen/_18sp" />

    </LinearLayout>

    <com.pierfrancescosoffritti.androidyoutubeplayer.core.player.views.YouTubePlayerView
        android:id="@+id/youtube_player_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:fontFamily="@font/font_700"
        android:textColor="@color/white"
        android:maxLines="1"
        android:ellipsize="end"
        android:textSize="@dimen/_18sp"
        app:layout_constraintEnd_toEndOf="@id/youtube_player_view"
        app:layout_constraintStart_toStartOf="@id/youtube_player_view"
        app:layout_constraintTop_toBottomOf="@id/youtube_player_view"
        tools:text="Street camera 01" />

    <LinearLayout
        android:id="@+id/ll_view_count"
        android:layout_width="0dp"
        android:gravity="center_vertical"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="@id/youtube_player_view"
        app:layout_constraintStart_toStartOf="@id/youtube_player_view"
        app:layout_constraintTop_toBottomOf="@id/tv_name">

        <ImageView
            android:layout_width="@dimen/_20dp"
            android:layout_height="@dimen/_20dp"
            android:src="@drawable/ic_eye" />

        <TextView
            android:id="@+id/tv_count"
            android:layout_marginStart="@dimen/_7dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:ellipsize="end"
            android:fontFamily="@font/font_500"
            tools:text="100,000,000 views"
            android:textColor="@color/white"
            android:textSize="@dimen/_12sp" />
    </LinearLayout>

    <com.tistory.zladnrms.roundablelayout.RoundableLayout
        app:cornerAll="@dimen/_10dp"
        android:id="@+id/layoutAdsTop"
        android:layout_width="match_parent"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_height="0dp"
       app:layout_constraintTop_toBottomOf="@id/ll_view_count"
        app:layout_constraintDimensionRatio="360:70"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:id="@+id/adViewGroupTop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/grey_a1"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black" />

        </FrameLayout>

    </com.tistory.zladnrms.roundablelayout.RoundableLayout>

    <TextView
        android:id="@+id/tv_recommend"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_20dp"
        android:layout_marginTop="@dimen/_16dp"
        android:fontFamily="@font/font_700"
        android:text="@string/other_video_live"
        android:textColor="@color/white"
        android:textSize="@dimen/_18sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutAdsTop" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_main"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_15dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:orientation="vertical"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/tv_recommend"
        tools:listitem="@layout/item_video_live" />

    <FrameLayout
        android:id="@+id/full_screen_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>