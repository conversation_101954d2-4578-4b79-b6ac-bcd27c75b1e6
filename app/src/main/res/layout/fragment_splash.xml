<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/img_splash_background">

    <ImageView
        android:id="@+id/iconApp"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="H,168:58"
        app:layout_constraintEnd_toEndOf="parent"
        android:src="@drawable/img_camera_gps_title"
        android:scaleType="fitCenter"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.60"
        app:layout_constraintWidth_percent="0.47" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="@dimen/_20dp"
        app:layout_constraintTop_toBottomOf="@id/iconApp"/>

    <TextView
        android:id="@+id/txvLoading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/_8dp"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:textAlignment="center"
        android:fontFamily="@font/font_300"
        android:textColor="@color/gray_808080"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/fullscreen_ad_might_shown"
        android:textSize="@dimen/text_size_16"
        app:layout_constraintTop_toBottomOf="@id/progressBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>