<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_marginTop="@dimen/_6dp"
            android:padding="@dimen/_10dp"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_7dp"
            android:fontFamily="@font/font_700"
            android:text="@string/edit_location"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_18sp"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="@id/btnBack" />

        <!-- Done button -->
        <TextView
            android:id="@+id/btnDone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/blue_3e9df4"
            android:fontFamily="@font/font_700"
            android:paddingHorizontal="@dimen/_12dp"
            android:paddingVertical="@dimen/_8dp"
            android:text="@string/done"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="@dimen/_16sp"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/btnBack" />

        <LinearLayout
            android:id="@+id/llButtonTabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constraintTop_toBottomOf="@id/btnBack">

            <LinearLayout
                android:id="@+id/llButtonTabChangeLocation"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvButtonTabChangeLocation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/tab_change_location"
                    android:textColor="@color/blue_28abf5"
                    android:textSize="@dimen/_16sp" />

                <View
                    android:id="@+id/vSelectedTabChangeLocation"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_2dp"
                    android:background="@color/blue_28abf5"
                    android:visibility="visible" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/llButtonTabEditLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvButtonTabEditLayout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:layout_marginBottom="@dimen/_8dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/tab_change_layout"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_16sp" />

                <View
                    android:id="@+id/vSelectedTabEditLayout"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_2dp"
                    android:background="@android:color/transparent"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clChangeLocation"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/_10dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintTop_toBottomOf="@id/llButtonTabs">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <!-- Map Card -->
                    <FrameLayout
                        android:id="@+id/fl_card_current_location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardCurrentLocation"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_200dp"
                            android:layout_margin="@dimen/_16dp"
                            app:cardCornerRadius="@dimen/_16dp"
                            app:strokeColor="@color/blue_3e9df4"
                            app:strokeWidth="@dimen/_2dp">

                            <FrameLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <fragment
                                    android:id="@+id/map"
                                    android:name="com.google.android.gms.maps.SupportMapFragment"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent" />

                                <TextView
                                    android:id="@+id/currentLocationLabel"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="top|center_horizontal"
                                    android:background="@drawable/bg_label_current_location"
                                    android:fontFamily="@font/font_600"
                                    android:paddingHorizontal="@dimen/_10dp"
                                    android:paddingVertical="@dimen/_6dp"
                                    android:text="@string/label_current_location"
                                    android:textColor="@android:color/white"
                                    android:textSize="@dimen/_15sp" />

                                <!-- Overlay -->
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="bottom"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:background="@drawable/bg_overlay_camera_gps_screen"
                                        android:paddingVertical="@dimen/_6dp">

                                        <co.cameralocation.customview.RoundedImageView
                                            android:layout_width="@dimen/_48dp"
                                            android:layout_height="@dimen/_48dp"
                                            android:layout_marginStart="@dimen/_12dp"
                                            android:src="@drawable/img_location_icon_default"
                                            app:cornerRadius="@dimen/_6dp" />

                                        <LinearLayout
                                            android:layout_width="0dp"
                                            android:layout_height="wrap_content"
                                            android:layout_marginStart="@dimen/_8dp"
                                            android:layout_weight="1"
                                            android:orientation="vertical">

                                            <TextView
                                                android:id="@+id/currentLocationName"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:ellipsize="marquee"
                                                android:focusable="true"
                                                android:focusableInTouchMode="true"
                                                android:fontFamily="@font/font_400"
                                                android:marqueeRepeatLimit="marquee_forever"
                                                android:scrollHorizontally="true"
                                                android:singleLine="true"
                                                android:text="@string/sample_location_name"
                                                android:textColor="@android:color/white"
                                                android:textSize="@dimen/_14sp" />

                                            <TextView
                                                android:id="@+id/currentLocationCoordinates"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/font_400"
                                                android:text="@string/sample_coordinates"
                                                android:textColor="@android:color/white"
                                                android:textSize="@dimen/_12sp" />

                                            <TextView
                                                android:id="@+id/currentLocationTimestamp"
                                                android:layout_width="match_parent"
                                                android:layout_height="wrap_content"
                                                android:fontFamily="@font/font_400"
                                                android:text="@string/sample_timestamp"
                                                android:textColor="@android:color/white"
                                                android:textSize="@dimen/_12sp" />
                                        </LinearLayout>

                                        <ImageView
                                            android:id="@+id/btnRefresh"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_gravity="center"
                                            android:padding="@dimen/_10dp"
                                            android:src="@drawable/ic_refresh"
                                            app:tint="@android:color/white" />
                                    </LinearLayout>

                                </LinearLayout>
                            </FrameLayout>
                        </com.google.android.material.card.MaterialCardView>

                    </FrameLayout>

                    <com.tistory.zladnrms.roundablelayout.RoundableLayout
                        android:id="@+id/layoutAdsChangeLocationTop"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginHorizontal="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_10dp"
                        app:cornerAll="@dimen/_10dp"
                        app:layout_constraintDimensionRatio="360:70"
                        app:layout_constraintTop_toBottomOf="@id/fl_card_current_location">

                        <FrameLayout
                            android:id="@+id/adViewGroupChangeLocationTop"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="bottom"
                            android:background="#D7D6D6">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:fontFamily="@font/font_400"
                                android:gravity="center"
                                android:text="@string/loading_ads"
                                android:textColor="@color/black"
                                android:textSize="@dimen/_16sp" />

                        </FrameLayout>
                    </com.tistory.zladnrms.roundablelayout.RoundableLayout>

                    <!-- Recent Locations Title -->
                    <TextView
                        android:id="@+id/recentLocationTitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_24dp"
                        android:fontFamily="@font/font_600"
                        android:text="@string/title_recent_location"
                        android:textColor="@color/blue_28abf5"
                        android:textSize="@dimen/_16sp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/layoutAdsChangeLocationTop" />

                    <!-- Add location button -->
                    <TextView
                        android:id="@+id/btnAddLocation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/minus_5dp"
                        android:layout_marginEnd="@dimen/_6dp"
                        android:background="@drawable/bg_btn_add_location_edit_location_screen"
                        android:fontFamily="@font/font_600"
                        android:paddingHorizontal="@dimen/_10dp"
                        android:paddingVertical="@dimen/_5dp"
                        android:text="@string/add_new"
                        android:textColor="@color/blue_28abf5"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@id/recentLocationTitle" />

                    <!-- RecyclerView for Recent Locations -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recentLocationRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_8dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/recentLocationTitle"
                        tools:itemCount="3"
                        tools:listitem="@layout/layout_item_recent_location_camera_gps" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clEditLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintTop_toBottomOf="@id/llButtonTabs">

            <!-- RecyclerView for Change Layout Tab -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/editLayoutRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>