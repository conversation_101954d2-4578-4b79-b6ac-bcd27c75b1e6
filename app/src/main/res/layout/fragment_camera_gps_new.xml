<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <!-- Header with Title and Home button -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/headerLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_10dp"
                android:fontFamily="@font/font_700"
                android:text="@string/camera_gps"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_20sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/btnHome"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/btnHome"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/_8dp"
                android:src="@drawable/ic_home"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Main Content -->

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/_10dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintTop_toBottomOf="@id/headerLayout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:padding="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/headerLayout">

                <ImageView
                    android:id="@+id/ivEarth"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:src="@drawable/img_earth_camera_gps_new_screen"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.55" />

                <com.tistory.zladnrms.roundablelayout.RoundableLayout
                    android:id="@+id/layoutAdsTop"
                    android:layout_marginTop="@dimen/_24dp"
                    app:cornerAll="@dimen/_10dp"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintDimensionRatio="360:70"
                    app:layout_constraintTop_toBottomOf="@id/ivEarth">

                    <FrameLayout
                        android:id="@+id/adViewGroupTop"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="bottom"
                        android:background="#D7D6D6">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="@font/font_400"
                            android:gravity="center"
                            android:text="@string/loading_ads"
                            android:textColor="@color/black"
                            android:textSize="@dimen/_16sp" />

                    </FrameLayout>
                </com.tistory.zladnrms.roundablelayout.RoundableLayout>

                <!-- Camera GPS Button -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/btnCameraGps"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_24dp"
                    android:background="@drawable/bg_button_grid_photo"
                    android:padding="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layoutAdsTop">

                    <ImageView
                        android:id="@+id/ivCameraGps"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_camera_gps_new_screen"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@android:color/white" />

                    <TextView
                        android:id="@+id/tvCameraGps"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:fontFamily="@font/font_700"
                        android:text="@string/camera_gps"
                        android:textAllCaps="true"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivCameraGpsArrow"
                        app:layout_constraintStart_toEndOf="@id/ivCameraGps"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/ivCameraGpsArrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@android:color/white" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- Grid Photo Button -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/btnGridPhoto"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/bg_button_grid_photo"
                    android:padding="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btnCameraGps">

                    <ImageView
                        android:id="@+id/ivGridPhoto"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_grid_photo_camera_gps_new_screen"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/white" />

                    <TextView
                        android:id="@+id/tvGridPhoto"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:fontFamily="@font/font_500"
                        android:text="@string/grid_photo"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivGridPhotoArrow"
                        app:layout_constraintStart_toEndOf="@id/ivGridPhoto"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/ivGridPhotoArrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/white" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- Save Media Button -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/btnSaveMedia"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/bg_button_grid_photo"
                    android:padding="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btnGridPhoto">

                    <ImageView
                        android:id="@+id/ivSaveMedia"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_save_media_camera_gps_new_screen"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/white" />

                    <TextView
                        android:id="@+id/tvSaveMedia"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:fontFamily="@font/font_500"
                        android:text="@string/save_media"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/ivSaveMediaArrow"
                        app:layout_constraintStart_toEndOf="@id/ivSaveMedia"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/ivSaveMediaArrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_right"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:tint="@color/white" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
