<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_6dp"
            android:fontFamily="@font/font_700"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="@dimen/_18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Preview Photo" />

        <FrameLayout
            android:id="@+id/flContainer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/_6dp"
            android:layout_marginBottom="@dimen/_10dp"
            app:layout_constraintBottom_toTopOf="@id/llButtonsBottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle">

            <co.cameralocation.customview.RoundedImageView
                android:id="@+id/ivCapturedImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/_2dp"
                android:scaleType="centerCrop"
                app:cornerRadius="@dimen/_4dp" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clVideoContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/_2dp"
                android:visibility="visible">

                <androidx.media3.ui.PlayerView
                    android:id="@+id/player_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="@dimen/_10dp"
                    app:layout_constraintBottom_toTopOf="@id/playback_controls"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:resize_mode="fit"
                    app:show_buffering="when_playing"
                    app:show_shuffle_button="true" />

                <LinearLayout
                    android:id="@+id/playback_controls"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="@dimen/_8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/player_view">

                    <ImageView
                        android:id="@+id/button_play"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:focusable="true"
                        android:padding="@dimen/_6dp"
                        android:src="@drawable/ic_resume_video_recording_camera_screen"
                        app:tint="@android:color/white" />

                    <TextView
                        android:id="@+id/text_current_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_600"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_10sp"
                        tools:text="00:00" />

                    <androidx.appcompat.widget.AppCompatSeekBar
                        android:id="@+id/seek_bar"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginEnd="@dimen/_8dp"
                        android:layout_weight="1"
                        android:maxHeight="@dimen/_4dp"
                        android:minHeight="@dimen/_4dp"
                        android:progressDrawable="@drawable/custom_seekbar_track_camera_result_screen"
                        android:thumb="@drawable/custom_thumb_video_camera_result_screen"
                        tools:progress="20" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>

        <LinearLayout
            android:id="@+id/llButtonsBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:layout_marginBottom="@dimen/_10dp"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/_30dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="@color/black_192234"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_10dp"
                    android:src="@drawable/ic_delete_edit_media_screen"
                    app:tint="@color/red_f44336" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_10dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/delete"
                    android:textColor="@color/red_f44336"
                    android:textSize="@dimen/_12sp" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/btnAccept"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_weight="1"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="@color/black_192234"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/_10dp"
                    android:src="@drawable/ic_save_as_edit_media_screen"
                    app:tint="@color/green_8fcc81" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_10dp"
                    android:fontFamily="@font/font_600"
                    android:text="@string/save"
                    android:textColor="@color/green_8fcc81"
                    android:textSize="@dimen/_12sp" />
            </LinearLayout>

        </LinearLayout>

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
