<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#051022">

    <LinearLayout
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_55dp"
        android:background="#051022"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/_44dp"
            android:layout_height="match_parent"
            android:paddingVertical="@dimen/_15dp"
            android:paddingStart="@dimen/_20dp"
            android:src="@drawable/ic_back" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20dp"
            android:fontFamily="@font/font_700"
            android:text="@string/map_video_live"
            android:textColor="@color/white"
            android:textSize="@dimen/_18sp" />
    </LinearLayout>

    <fragment
        android:id="@+id/google_map"
        android:name="com.google.android.gms.maps.SupportMapFragment"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <ImageView
        android:id="@+id/btn_map_style"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:layout_marginTop="@dimen/_30dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:background="@drawable/bg_radius_300"
        android:backgroundTint="@color/white"
        android:paddingHorizontal="@dimen/_6dp"
        android:src="@drawable/ic_map_style"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_view" />

    <LinearLayout
        android:id="@+id/zoomContainer"
        android:layout_width="@dimen/_27dp"
        android:layout_height="@dimen/_73dp"
        android:layout_marginEnd="@dimen/_21dp"
        android:background="@drawable/bg_radius_47"
        android:backgroundTint="#FFFFFF"
        android:orientation="vertical"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <TextView
            android:id="@+id/btn_zoom_in"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:text="+"
            android:textColor="#252836"
            android:textSize="@dimen/_18sp" />

        <TextView
            android:id="@+id/btn_zoom_out"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fontFamily="@font/font_700"
            android:gravity="center"
            android:text="-"
            android:textColor="#252836"
            android:textSize="@dimen/_18sp" />
    </LinearLayout>


    <ImageView
        android:id="@+id/btn_current_position"
        android:layout_width="@dimen/_48dp"
        android:layout_height="@dimen/_48dp"
        android:layout_marginEnd="@dimen/_20dp"
        android:layout_marginBottom="@dimen/_20dp"
        android:background="@drawable/bg_radius_300"
        android:backgroundTint="@color/white"
        android:padding="@dimen/_12dp"
        android:src="@drawable/ic_my_point"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintEnd_toEndOf="parent" />

    <co.cameralocation.customview.search_location_view.SearchLocationView
        android:id="@+id/search_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_21dp"
        android:layout_marginTop="@dimen/_15dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />


    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#CFCFCF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/loading_ads"
                android:textColor="@color/black" />

        </FrameLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>