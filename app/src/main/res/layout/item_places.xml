<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/_16dp">

        <TextView
            android:id="@+id/place_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="@dimen/_16sp"
            android:fontFamily="@font/font_700"
            android:textColor="@color/black"
            tools:text="LOL Park"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/place_address"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_400"
            android:textColor="#73000000"
            tools:text="123 Main St, Anytown, USA"
            android:textSize="@dimen/_14sp" />
    </LinearLayout>
</layout>