<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/_8dp"
        android:background="@drawable/bg_radius_16"
        android:backgroundTint="@color/black_192234"
        android:gravity="center_vertical"
        android:orientation="vertical"
        android:padding="@dimen/_8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <co.cameralocation.customview.RoundedImageView
                android:layout_width="@dimen/_56dp"
                android:layout_height="@dimen/_56dp"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/_12dp"
                android:src="@drawable/img_location_icon_default"
                app:cornerRadius="@dimen/_6dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_6dp"
                android:paddingVertical="@dimen/_6dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/currentLocationName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        android:text="@string/sample_location_name"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_14sp" />

                    <TextView
                        android:id="@+id/currentLocationCoordinates"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        android:text="@string/sample_coordinates"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_12sp" />

                    <TextView
                        android:id="@+id/currentLocationTimestamp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        android:text="@string/sample_timestamp"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_12sp" />
                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp">

            <TextView
                android:id="@+id/btnDelete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="@color/black_121825"
                android:drawableStart="@drawable/ic_delete_edit_media_screen"
                android:drawablePadding="@dimen/_6dp"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:fontFamily="@font/font_600"
                android:marqueeRepeatLimit="marquee_forever"
                android:padding="@dimen/_10dp"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:text="@string/delete"
                android:textColor="@color/white"
                android:textSize="@dimen/_15sp" />

            <TextView
                android:id="@+id/btnEdit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/_10dp"
                android:layout_weight="1"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="@color/black_121825"
                android:drawableStart="@drawable/ic_pencil_edit_location_new"
                android:drawablePadding="@dimen/_6dp"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:fontFamily="@font/font_600"
                android:marqueeRepeatLimit="marquee_forever"
                android:padding="@dimen/_10dp"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:text="@string/edit"
                android:textColor="@color/white"
                android:textSize="@dimen/_15sp" />

            <TextView
                android:id="@+id/btnUse"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="@color/black_121825"
                android:drawableStart="@drawable/ic_next_language_screen"
                android:drawablePadding="@dimen/_6dp"
                android:ellipsize="marquee"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:fontFamily="@font/font_600"
                android:marqueeRepeatLimit="marquee_forever"
                android:padding="@dimen/_10dp"
                android:scrollHorizontally="true"
                android:singleLine="true"
                android:text="@string/recent_location_use"
                android:textColor="@color/blue_3e9df4"
                android:textSize="@dimen/_15sp" />

        </LinearLayout>

    </LinearLayout>
</layout>
