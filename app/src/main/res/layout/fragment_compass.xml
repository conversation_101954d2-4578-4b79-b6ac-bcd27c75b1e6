<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_121825">

    <!-- Header with Title and Home button -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/headerLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_16dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_10dp"
            android:fontFamily="@font/font_700"
            android:text="@string/compass"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_20sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnHome"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/btnHome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/_8dp"
            android:src="@drawable/ic_home"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutButtonSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_22dp"

        android:layout_marginBottom="70dp"
        android:background="@drawable/item_background_edit_text_title"
        android:padding="@dimen/_10dp"
        app:layout_constraintTop_toBottomOf="@+id/headerLayout">

        <ImageView
            android:id="@+id/imgLocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@null"
            android:src="@drawable/icon_location"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvSearch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:maxWidth="@dimen/_250dp"
            android:singleLine="true"
            android:text="@string/search_here"
            android:textColor="@color/text_color_hint"
            android:textSize="@dimen/_16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/imgLocation"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_24dp"
        android:layout_marginBottom="@dimen/_50dp"
        app:layout_constraintBottom_toTopOf="@+id/layoutAds"
        app:layout_constraintTop_toBottomOf="@+id/layoutButtonSearch" />

    <me.relex.circleindicator.CircleIndicator3
        android:id="@+id/indicator"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_10dp"
        android:layout_marginTop="@dimen/_10dp"
        app:ci_drawable="@drawable/border_indicator"
        app:layout_constraintTop_toBottomOf="@+id/viewPager" />

    <FrameLayout
        android:id="@+id/layoutAds"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="360:70"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <FrameLayout
            android:id="@+id/adViewGroup"
            android:background="@color/grey_a1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/loading_ads" />

        </FrameLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>