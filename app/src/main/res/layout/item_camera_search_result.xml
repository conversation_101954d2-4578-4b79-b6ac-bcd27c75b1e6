<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_marginBottom="@dimen/_12dp"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_radius_12"
            android:backgroundTint="#252836"
            android:paddingVertical="@dimen/_12dp">

            <com.makeramen.roundedimageview.RoundedImageView
                android:id="@+id/iv_main"
                android:layout_width="@dimen/_64dp"
                android:layout_height="@dimen/_48dp"
                android:layout_marginStart="@dimen/_16dp"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:riv_corner_radius="@dimen/_3dp"
                tools:src="@mipmap/ic_launcher" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:background="@drawable/bg_radius_2"
                android:backgroundTint="#FF2525"
                android:gravity="center_vertical"
                android:layout_marginTop="@dimen/_3dp"
                android:layout_marginStart="@dimen/_4dp"
                app:layout_constraintStart_toStartOf="@id/iv_main"
                app:layout_constraintTop_toTopOf="@id/iv_main"
                android:paddingHorizontal="@dimen/_4dp"
                android:paddingVertical="@dimen/_2dp"
                android:textColor="@color/white">

                <ImageView
                    android:layout_width="@dimen/_2dp"
                    android:layout_height="@dimen/_2dp"
                    android:src="@drawable/ic_round" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_2dp"
                    android:fontFamily="@font/font_700"
                    android:text="@string/live"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_4sp" />
            </LinearLayout>

            <ImageView
                android:layout_width="@dimen/_17dp"
                android:layout_height="@dimen/_17dp"
                android:src="@drawable/ic_play_camera_result"
                app:layout_constraintBottom_toBottomOf="@id/iv_main"
                app:layout_constraintEnd_toEndOf="@id/iv_main"
                app:layout_constraintStart_toStartOf="@id/iv_main"
                app:layout_constraintTop_toTopOf="@id/iv_main" />

            <TextView
                app:layout_constraintEnd_toEndOf="parent"
                android:id="@+id/tv_camera_name"
                android:layout_width="0dp"
                android:layout_marginEnd="@dimen/_10dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_13dp"
                android:fontFamily="@font/font_500"
                android:text="Street camera 01"
                android:textColor="@color/white"
                android:textSize="@dimen/_14sp"
                app:layout_constraintBottom_toBottomOf="@id/iv_main"
                app:layout_constraintStart_toEndOf="@id/iv_main"
                app:layout_constraintTop_toTopOf="@id/iv_main" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </FrameLayout>
</layout>