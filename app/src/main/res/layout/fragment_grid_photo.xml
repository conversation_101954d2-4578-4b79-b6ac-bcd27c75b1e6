<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_marginTop="@dimen/_6dp"
            android:padding="@dimen/_10dp"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_7dp"
            android:fontFamily="@font/font_700"
            android:maxWidth="@dimen/_240dp"
            android:text="@string/photo_layout"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_18sp"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/btnBack" />

        <!-- Tab Layout -->
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:background="@color/black_121825"
            app:layout_constraintTop_toBottomOf="@id/titleText"
            app:tabMode="scrollable"
            app:tabIndicatorColor="@color/blue_3e9df4"
            app:tabSelectedTextColor="@color/blue_3e9df4"
            app:tabTextColor="@android:color/white" />

        <!-- ViewPager for Tabs -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintTop_toBottomOf="@id/tabLayout" />

        <!-- Orientation Selection RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvOrientations"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_16dp"
            android:clipToPadding="false"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tabLayout"
            tools:listitem="@layout/item_grid_orientation" />

        <!-- Orientation Title -->
        <TextView
            android:id="@+id/tvOrientationTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_16dp"
            android:fontFamily="@font/font_700"
            android:text="@string/select_orientation"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_16sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tabLayout" />

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
