<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/img_background_home">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_marginTop="@dimen/_6dp"
            android:padding="@dimen/_10dp"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/edit_location"
            android:textSize="@dimen/_18sp"
            android:textColor="@android:color/white"
            android:fontFamily="@font/font_700"
            android:layout_marginStart="@dimen/_7dp"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="@id/btnBack"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"
            app:layout_constraintHorizontal_bias="0" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleCurrentLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/label_current_location"
            android:textSize="@dimen/_16sp"
            android:textColor="@android:color/white"
            android:fontFamily="@font/font_400"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleText"
            app:layout_constraintEnd_toEndOf="parent"
            android:textAlignment="center"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginHorizontal="@dimen/_16dp" />

        <!-- Current Location Item -->
        <FrameLayout
            android:id="@+id/flCurrentLocationItem"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleCurrentLocation"
            app:layout_constraintBottom_toTopOf="@id/divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintDimensionRatio="H,312:113"
            app:layout_constraintWidth_percent="1"
            android:layout_marginVertical="@dimen/_10dp" />

        <ImageView
            android:id="@+id/divider"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:src="@drawable/img_line_edit_location"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/flCurrentLocationItem"
            android:layout_marginTop="@dimen/_19dp"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleRecentLocation"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/title_recent_location"
            android:textSize="@dimen/_16sp"
            android:textColor="@android:color/white"
            android:fontFamily="@font/font_400"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider"
            app:layout_constraintEnd_toStartOf="@id/btnAddLocation"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginHorizontal="@dimen/_16dp" />

        <!-- Add location button -->
        <ImageView
            android:id="@+id/btnAddLocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_6dp"
            android:padding="@dimen/_5dp"
            android:layout_marginTop="@dimen/minus_5dp"
            android:src="@drawable/ic_add_location_edit_location_screen"
            app:layout_constraintEnd_toEndOf="parent"
            app:tint="@android:color/white"
            app:layout_constraintTop_toTopOf="@id/titleRecentLocation" />

        <!-- RecyclerView for Recent Locations -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recentLocationRecyclerView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:textSize="@dimen/_16sp"
            android:textColor="@android:color/white"
            android:fontFamily="@font/font_400"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/titleRecentLocation"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintEnd_toEndOf="parent"
            android:textAlignment="center"
            android:layout_marginTop="@dimen/_16dp"
            android:layout_marginHorizontal="@dimen/_16dp" />

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
