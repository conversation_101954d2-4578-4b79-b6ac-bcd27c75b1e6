<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_marginTop="@dimen/_6dp"
            android:padding="@dimen/_10dp"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_7dp"
            android:fontFamily="@font/font_700"
            android:text="@string/edit_grid"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_18sp"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="@id/btnBack" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="@dimen/_10dp"
            app:layout_constraintTop_toBottomOf="@id/btnBack"
            app:layout_constraintBottom_toTopOf="@id/layoutAds">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:id="@+id/flGridLayout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/_10dp"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="bottom">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:fontFamily="@font/font_400"
                            android:gravity="center"
                            android:text="@string/content_is_loading"
                            android:textColor="@color/white"
                            android:textSize="@dimen/_16sp" />

                    </FrameLayout>

                    <co.cameralocation.customview.GridLayoutView
                        android:id="@+id/gridLayoutView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </FrameLayout>

                <LinearLayout
                    android:id="@+id/llButtonsBottom"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/flGridLayout"
                    android:layout_marginTop="@dimen/_20dp">

                    <LinearLayout
                        android:id="@+id/llEditLocation"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_20dp"
                        android:layout_weight="1"
                        android:background="@drawable/bg_radius_14"
                        android:backgroundTint="@color/black_192234"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/_10dp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:src="@drawable/ic_edit_location" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/_8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_600"
                            android:text="@string/edit_location"
                            android:textAlignment="center"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/_14sp" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/llEditGrid"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/_20dp"
                        android:layout_weight="1"
                        android:background="@drawable/bg_radius_14"
                        android:backgroundTint="@color/black_192234"
                        android:orientation="vertical"
                        android:paddingVertical="@dimen/_10dp">

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_horizontal"
                            android:src="@drawable/ic_grid_photo_camera_gps_screen" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="0dp"
                            android:layout_marginTop="@dimen/_8dp"
                            android:layout_weight="1"
                            android:fontFamily="@font/font_600"
                            android:text="@string/edit_grid"
                            android:textAlignment="center"
                            android:textColor="@android:color/white"
                            android:textSize="@dimen/_14sp" />
                    </LinearLayout>

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnSaveAs"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_14dp"
                    android:layout_marginHorizontal="@dimen/_20dp"
                    android:background="@drawable/bg_button_primary"
                    android:backgroundTint="@color/blue_28abf5"
                    android:text="@string/save"
                    android:textSize="@dimen/_16sp"
                    android:fontFamily="@font/font_700"
                    android:textColor="@android:color/white"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/llButtonsBottom" />

                <!-- Loading Indicator -->
                <ProgressBar
                    android:id="@+id/progressLoading"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
