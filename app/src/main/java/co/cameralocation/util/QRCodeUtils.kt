package co.cameralocation.util

import android.graphics.Bitmap
import android.graphics.Color
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import androidx.core.graphics.createBitmap
import androidx.core.graphics.set
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo
import kotlin.apply
import kotlin.ranges.until

object QRCodeUtils {
    
    /**
     * Tạo QR code từ dữ liệu vị trí
     * @param locationInfo Thông tin vị trí
     * @param size Kích thước của QR code
     * @return Bitmap chứa QR code
     */
    suspend fun generateQRCode(locationInfo: LocationInfo, size: Int = 300): Bitmap = withContext(Dispatchers.Default) {
        try {
            // Tạo chuỗi dữ liệu từ LocationInfo
            val locationData = "geo:${locationInfo.latitude},${locationInfo.longitude}?q=${locationInfo.name}"
            
            val hints = hashMapOf<EncodeHintType, Any>().apply {
                put(EncodeHintType.CHARACTER_SET, "UTF-8")
                put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H)
                put(EncodeHintType.MARGIN, 1)
            }
            
            val writer = QRCodeWriter()
            val bitMatrix = writer.encode(locationData, BarcodeFormat.QR_CODE, size, size, hints)
            
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bitmap = createBitmap(width, height)
            
            for (x in 0 until width) {
                for (y in 0 until height) {
                    bitmap[x, y] = if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
                }
            }
            
            bitmap
        } catch (e: Exception) {
            // Trả về bitmap trống nếu có lỗi
            createBitmap(size, size).apply {
                eraseColor(Color.WHITE)
            }
        }
    }
}