package co.cameralocation.util

import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import co.cameralocation.MyApplication
import java.io.File
import java.io.FileOutputStream
import kotlin.apply
import kotlin.collections.forEach
import kotlin.io.extension
import kotlin.io.nameWithoutExtension
import kotlin.text.replace

fun getDownloadYoutubeFolder(): File {
    val path =
        "${Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)}/TattooMaker"
    val folder = File(path)
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}

fun getMusicNetworkFolder(context: Context): File {
    val folder = File(context.filesDir, "MusicNetwork")
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}

fun getUniqueFilePath(destinationFilePath: String): String {
    val file = File(destinationFilePath)
    if (!file.exists()) return destinationFilePath.replace(" ", "")

    val parentDir = file.parent ?: ""
    val fileName = file.nameWithoutExtension.replace(" ", "")
    val extension = file.extension

    var newFile = file
    var index = 1
    while (newFile.exists()) {
        val newFileName = "$fileName($index).$extension"
        newFile = File(parentDir, newFileName.replace(" ", ""))
        index++
    }

    return newFile.absolutePath
}


fun getMusicTempFolder(context: Context): File {
    val folder = File(context.cacheDir, "TempMusic")
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}

fun getVideoTempFolder(context: Context): File {
    val folder = File(context.cacheDir, "TempVideo")
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}

fun getImageTempFolder(context: Context): File {
    val folder = File(context.cacheDir, "TempImage")
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}

fun getCameraTattooSaveMediaFolder(): File {
    val path =
        "${Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM)}/TattooMaker"
    val folder = File(path)
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}

fun getCameraTattooSaveMediaDirFolder(context: Context): File {
    val folder = File(context.filesDir, "CameraTattooImage")
    if (!folder.exists()) {
        folder.mkdirs()
    }
    return folder
}


fun Context.createImageUri(): Uri? {
    val contentValues = ContentValues().apply {
        put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")

        // Kiểm tra phiên bản hệ điều hành
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/MyApp")
        } else {
            val directory =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                    .toString() + "/MyApp"
            val file = File(directory)
            if (!file.exists()) file.mkdirs() // Tạo thư mục nếu chưa tồn tại
            put(MediaStore.Images.Media.DATA, "$directory/${System.currentTimeMillis()}.jpg")
        }
    }
    return this.contentResolver.insert(
        MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
        contentValues
    )
}

//for download image
val internalFolder = File(MyApplication.getApplicationContext().filesDir, "TattooMakerFolder")
val tempImageFolder = "$internalFolder/tempImage"
val imageDownloadedFolder = "$internalFolder/imageDownloaded"
val stickerDownloadedFolder = "$internalFolder/stickerDownloaded"
val imageStickerUploaded = "$internalFolder/imageStickerUploaded"

fun createAllTempFolder() {
    runCatching {
        if (!internalFolder.exists()) {
            internalFolder.mkdirs()
        }

        if (!File(tempImageFolder).exists()) {
            File(tempImageFolder).mkdirs()
        }

        if (!File(imageDownloadedFolder).exists()) {
            File(imageDownloadedFolder).mkdirs()
        }

        if (!File(imageStickerUploaded).exists()) {
            File(imageStickerUploaded).mkdirs()
        }

        if (!File(stickerDownloadedFolder).exists()) {
            File(stickerDownloadedFolder).mkdirs()
        }
    }
}

fun clearFolder(folder: File) {
    if (folder.exists() && folder.isDirectory) {
        folder.listFiles()?.forEach { file ->
            if (file.isFile) {
                file.delete()
            }
        }
    }
}

fun Context.saveBitmapToFileDir(folderPath: String, bitmap: Bitmap, fileName: String): File? {
    return try {
        // Tạo file trong thư mục filedir
        val file = File(folderPath, fileName)
        val outputStream = FileOutputStream(file)
        bitmap.compress(
            Bitmap.CompressFormat.PNG,
            100,
            outputStream
        ) // Nén và ghi bitmap vào file
        outputStream.flush()
        outputStream.close()
        file // Trả về file đã lưu
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun getImageDimensions(imagePath: String, width: Int, height: Int): IntArray {
    val exif = ExifInterface(imagePath)
    val orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL)

    return if (orientation == ExifInterface.ORIENTATION_ROTATE_90 ||
        orientation == ExifInterface.ORIENTATION_ROTATE_270) {
        intArrayOf(height, width) // Đảo ngược width và height
    } else {
        intArrayOf(width, height) // Giữ nguyên
    }
}