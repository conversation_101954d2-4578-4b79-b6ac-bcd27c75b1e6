package co.cameralocation.framework.repository

import kotlinx.coroutines.flow.Flow
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem

interface LocationInfoWithLayoutItemDirectRepository {
    
    suspend fun saveLocationInfoWithLayoutItem(locationInfoWithLayoutItem: LocationInfoWithLayoutItem): Long
    
    suspend fun updateLocationInfoWithLayoutItem(locationInfoWithLayoutItem: LocationInfoWithLayoutItem)
    
    suspend fun deleteLocationInfoWithLayoutItem(locationInfoWithLayoutItem: LocationInfoWithLayoutItem)
    
    fun getAllLocationInfoWithLayoutItemsFlow(): Flow<List<LocationInfoWithLayoutItem>>
    
    suspend fun getAllLocationInfoWithLayoutItems(): List<LocationInfoWithLayoutItem>
    
    suspend fun getLocationInfoWithLayoutItemById(id: Long): LocationInfoWithLayoutItem?
    
    suspend fun getLocationInfoWithLayoutItemsByLocationInfoId(locationInfoId: Long): List<LocationInfoWithLayoutItem>
    
    fun getLocationInfoWithLayoutItemsByLocationInfoIdFlow(locationInfoId: Long): Flow<List<LocationInfoWithLayoutItem>>
    
    suspend fun getLocationInfoWithLayoutItemsByLayoutItemId(layoutItemId: Long): List<LocationInfoWithLayoutItem>
    
    fun getLocationInfoWithLayoutItemsByLayoutItemIdFlow(layoutItemId: Long): Flow<List<LocationInfoWithLayoutItem>>
    
    suspend fun getDefaultLayoutItemForLocationInfo(locationInfoId: Long): LocationInfoWithLayoutItem?
    
    suspend fun setDefaultLayoutItemForLocationInfo(locationInfoId: Long, layoutItemId: Long)
    
    suspend fun deleteAllLocationInfoWithLayoutItems()
}
