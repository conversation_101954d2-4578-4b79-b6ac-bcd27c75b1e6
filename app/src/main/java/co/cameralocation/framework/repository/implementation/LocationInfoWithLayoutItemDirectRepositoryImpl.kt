package co.cameralocation.framework.repository.implementation

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import co.cameralocation.framework.database.converter.LocationInfoWithLayoutItemDirectConverter
import co.cameralocation.framework.database.daointerface.LocationInfoWithLayoutItemDirectDAO
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.framework.repository.LocationInfoWithLayoutItemDirectRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LocationInfoWithLayoutItemDirectRepositoryImpl @Inject constructor(
    private val locationInfoWithLayoutItemDirectDAO: LocationInfoWithLayoutItemDirectDAO,
    private val locationInfoWithLayoutItemDirectConverter: LocationInfoWithLayoutItemDirectConverter
) : LocationInfoWithLayoutItemDirectRepository {

    override suspend fun saveLocationInfoWithLayoutItem(locationInfoWithLayoutItem: LocationInfoWithLayoutItem): Long {
        try {
            val entity = locationInfoWithLayoutItemDirectConverter.toEntity(locationInfoWithLayoutItem)
            return locationInfoWithLayoutItemDirectDAO.insert(entity)
        } catch (e: Exception) {
            Timber.e("Error inserting LocationInfoWithLayoutItem: ${e.message}")
            return 0
        }
    }

    override suspend fun updateLocationInfoWithLayoutItem(locationInfoWithLayoutItem: LocationInfoWithLayoutItem) {
        try {
            val entity = locationInfoWithLayoutItemDirectConverter.toEntity(locationInfoWithLayoutItem)
            locationInfoWithLayoutItemDirectDAO.update(entity)
            Timber.d("Updated LocationInfoWithLayoutItem")
        } catch (e: Exception) {
            Timber.e("Error updating LocationInfoWithLayoutItem: ${e.message}")
        }
    }

    override suspend fun deleteLocationInfoWithLayoutItem(locationInfoWithLayoutItem: LocationInfoWithLayoutItem) {
        try {
            val entity = locationInfoWithLayoutItemDirectConverter.toEntity(locationInfoWithLayoutItem)
            locationInfoWithLayoutItemDirectDAO.delete(entity)
        } catch (e: Exception) {
            Timber.e("Error deleting LocationInfoWithLayoutItem: ${e.message}")
        }
    }

    override fun getAllLocationInfoWithLayoutItemsFlow(): Flow<List<LocationInfoWithLayoutItem>> {
        return locationInfoWithLayoutItemDirectDAO.getAllLocationInfoWithLayoutItemsFlow().map { entities ->
            locationInfoWithLayoutItemDirectConverter.toModelList(entities)
        }
    }

    override suspend fun getAllLocationInfoWithLayoutItems(): List<LocationInfoWithLayoutItem> {
        val entities = locationInfoWithLayoutItemDirectDAO.getAllLocationInfoWithLayoutItems()
        return locationInfoWithLayoutItemDirectConverter.toModelList(entities)
    }

    override suspend fun getLocationInfoWithLayoutItemById(id: Long): LocationInfoWithLayoutItem? {
        val entity = locationInfoWithLayoutItemDirectDAO.getLocationInfoWithLayoutItemById(id) ?: return null
        return locationInfoWithLayoutItemDirectConverter.toModel(entity)
    }

    override suspend fun getLocationInfoWithLayoutItemsByLocationInfoId(locationInfoId: Long): List<LocationInfoWithLayoutItem> {
        val entities = locationInfoWithLayoutItemDirectDAO.getLocationInfoWithLayoutItemsByLocationInfoId(locationInfoId)
        return locationInfoWithLayoutItemDirectConverter.toModelList(entities)
    }

    override fun getLocationInfoWithLayoutItemsByLocationInfoIdFlow(locationInfoId: Long): Flow<List<LocationInfoWithLayoutItem>> {
        return locationInfoWithLayoutItemDirectDAO.getLocationInfoWithLayoutItemsByLocationInfoIdFlow(locationInfoId).map { entities ->
            locationInfoWithLayoutItemDirectConverter.toModelList(entities)
        }
    }

    override suspend fun getLocationInfoWithLayoutItemsByLayoutItemId(layoutItemId: Long): List<LocationInfoWithLayoutItem> {
        val entities = locationInfoWithLayoutItemDirectDAO.getLocationInfoWithLayoutItemsByLayoutItemId(layoutItemId)
        return locationInfoWithLayoutItemDirectConverter.toModelList(entities)
    }

    override fun getLocationInfoWithLayoutItemsByLayoutItemIdFlow(layoutItemId: Long): Flow<List<LocationInfoWithLayoutItem>> {
        return locationInfoWithLayoutItemDirectDAO.getLocationInfoWithLayoutItemsByLayoutItemIdFlow(layoutItemId).map { entities ->
            locationInfoWithLayoutItemDirectConverter.toModelList(entities)
        }
    }

    override suspend fun getDefaultLayoutItemForLocationInfo(locationInfoId: Long): LocationInfoWithLayoutItem? {
        val entity = locationInfoWithLayoutItemDirectDAO.getDefaultLayoutItemForLocationInfo(locationInfoId) ?: return null
        return locationInfoWithLayoutItemDirectConverter.toModel(entity)
    }

    override suspend fun setDefaultLayoutItemForLocationInfo(locationInfoId: Long, layoutItemId: Long) {
        locationInfoWithLayoutItemDirectDAO.setDefaultLayoutItemForLocationInfo(locationInfoId, layoutItemId)
    }

    override suspend fun deleteAllLocationInfoWithLayoutItems() {
        locationInfoWithLayoutItemDirectDAO.deleteAllLocationInfoWithLayoutItems()
    }
}
