package co.cameralocation.framework.database.entities

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import kotlinx.parcelize.Parcelize
import co.cameralocation.framework.database.converter.LocationInfoTypeConverter
import co.cameralocation.framework.database.converter.LayoutItemTypeConverter
import co.cameralocation.framework.database.entities.LocationInfoWithLayoutItemDirectEntity.Companion.TABLE_NAME
import co.cameralocation.framework.presentation.editlocation.adapter.LayoutItem
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo

@Entity(tableName = TABLE_NAME)
@Parcelize
@TypeConverters(LocationInfoTypeConverter::class, LayoutItemTypeConverter::class)
data class LocationInfoWithLayoutItemDirectEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = ID)
    val id: Long = 0,

    @ColumnInfo(name = LOCATION_INFO)
    val locationInfo: LocationInfo,

    @ColumnInfo(name = LAYOUT_ITEM)
    val layoutItem: LayoutItem,

    @ColumnInfo(name = IS_DEFAULT)
    val isDefault: Boolean = false
) : Parcelable {

    companion object {
        const val TABLE_NAME = "LocationInfoWithLayoutItemDirect"
        const val ID = "id"
        const val LOCATION_INFO = "location_info"
        const val LAYOUT_ITEM = "layout_item"
        const val IS_DEFAULT = "is_default"
    }
}
