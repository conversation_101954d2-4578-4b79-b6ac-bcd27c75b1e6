package co.cameralocation.framework.database.entities

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import co.cameralocation.framework.database.entities.LayoutItemEntity.Companion.TABLE_NAME

@Entity(tableName = TABLE_NAME)
@Parcelize
data class LayoutItemEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = ID)
    val id: Long = 0,

    @ColumnInfo(name = NAME)
    val name: String,

    @ColumnInfo(name = LAYOUT_ITEM_TYPE)
    val layoutItemType: String,

    @ColumnInfo(name = DEFAULT_TITLE)
    val defaultTitle: String? = null,

    @ColumnInfo(name = IS_TITLE_VISIBLE)
    val isTitleVisible: Boolean = true,

    @ColumnInfo(name = IS_LOCATION_VISIBLE)
    val isLocationVisible: Boolean = true,

    @ColumnInfo(name = IS_COORDINATES_VISIBLE)
    val isCoordinatesVisible: Boolean = true,

    @ColumnInfo(name = IS_DATETIME_VISIBLE)
    val isDateTimeVisible: Boolean = true,

    @ColumnInfo(name = IS_WEATHER_VISIBLE, defaultValue = "1")
    val isWeatherVisible: Boolean = true,

    @ColumnInfo(name = IS_SELECTED)
    val isSelected: Boolean = false
) : Parcelable {

    companion object {
        const val TABLE_NAME = "LayoutItem"
        const val ID = "id"
        const val NAME = "name"
        const val LAYOUT_ITEM_TYPE = "layout_item_type"
        const val IS_TITLE_VISIBLE = "is_title_visible"
        const val IS_LOCATION_VISIBLE = "is_location_visible"
        const val IS_COORDINATES_VISIBLE = "is_coordinates_visible"
        const val IS_DATETIME_VISIBLE = "is_datetime_visible"
        const val IS_WEATHER_VISIBLE = "is_weather_visible"
        const val IS_SELECTED = "is_selected"
        const val DEFAULT_TITLE = "default_title"
    }
}
