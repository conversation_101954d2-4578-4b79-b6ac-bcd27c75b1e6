package co.cameralocation.framework.database.daointerface

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import co.cameralocation.framework.database.entities.LayoutItemEntity

@Dao
interface LayoutItemDAO {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(layoutItem: LayoutItemEntity): Long

    @Update
    suspend fun update(layoutItem: LayoutItemEntity)

    @Delete
    suspend fun delete(layoutItem: LayoutItemEntity)

    @Query("SELECT * FROM ${LayoutItemEntity.TABLE_NAME}")
    fun getAllLayoutItemsFlow(): Flow<List<LayoutItemEntity>>

    @Query("SELECT * FROM ${LayoutItemEntity.TABLE_NAME}")
    suspend fun getAllLayoutItems(): List<LayoutItemEntity>

    @Query("SELECT * FROM ${LayoutItemEntity.TABLE_NAME} WHERE ${LayoutItemEntity.ID} = :id")
    suspend fun getLayoutItemById(id: Long): LayoutItemEntity?

    @Query("SELECT * FROM ${LayoutItemEntity.TABLE_NAME} WHERE ${LayoutItemEntity.IS_SELECTED} = 1 LIMIT 1")
    suspend fun getSelectedLayoutItem(): LayoutItemEntity?

    @Query("UPDATE ${LayoutItemEntity.TABLE_NAME} SET ${LayoutItemEntity.IS_COORDINATES_VISIBLE} = :isVisible WHERE ${LayoutItemEntity.ID} = :id")
    suspend fun updateCoordinatesVisibility(id: Long, isVisible: Boolean)

    @Query("UPDATE ${LayoutItemEntity.TABLE_NAME} SET ${LayoutItemEntity.IS_DATETIME_VISIBLE} = :isVisible WHERE ${LayoutItemEntity.ID} = :id")
    suspend fun updateDateTimeVisibility(id: Long, isVisible: Boolean)

    @Query("UPDATE ${LayoutItemEntity.TABLE_NAME} SET ${LayoutItemEntity.IS_WEATHER_VISIBLE} = :isVisible WHERE ${LayoutItemEntity.ID} = :id")
    suspend fun updateWeatherVisibility(id: Long, isVisible: Boolean)

    @Query("UPDATE ${LayoutItemEntity.TABLE_NAME} SET ${LayoutItemEntity.DEFAULT_TITLE} = :title WHERE ${LayoutItemEntity.ID} = :id")
    suspend fun updateDefaultUnknownTitle(id: Long, title: String)

    @Query("UPDATE ${LayoutItemEntity.TABLE_NAME} SET ${LayoutItemEntity.IS_SELECTED} = 0")
    suspend fun clearSelectedLayoutItems()

    @Query("UPDATE ${LayoutItemEntity.TABLE_NAME} SET ${LayoutItemEntity.IS_SELECTED} = 1 WHERE ${LayoutItemEntity.ID} = :id")
    suspend fun setSelectedLayoutItem(id: Long)

    @Query("DELETE FROM ${LayoutItemEntity.TABLE_NAME}")
    suspend fun deleteAllLayoutItems()
}
