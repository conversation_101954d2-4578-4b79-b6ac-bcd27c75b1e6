package co.cameralocation.framework.presentation.editmediaphoto

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.core.graphics.createBitmap
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.CustomTarget
import com.bumptech.glide.request.transition.Transition
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import co.cameralocation.R
import co.cameralocation.customview.sticker.StickerImageView
import co.cameralocation.customview.sticker.StickerView
import co.cameralocation.framework.presentation.common.launchIO
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.editmediaphoto.bottomsheet.TextOverlayBottomSheet
import co.cameralocation.framework.presentation.editmediaphoto.bottomsheet.TextOverlayBottomSheetType
import co.cameralocation.framework.presentation.model.editmediaphoto.StickerModel
import co.cameralocation.framework.presentation.model.media.CameraGPSSavedMediaItem
import co.cameralocation.util.BitmapUtils
import co.cameralocation.util.captureAsBitmap
import co.cameralocation.util.convertDpToPx
import co.cameralocation.util.displayToast
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.util.setPreventDoubleClickScaleView
import co.cameralocation.util.showOkDialog

fun EditMediaPhotoFragment.addTextEvent() {
    binding.llEditText.setPreventDoubleClickScaleView {
        viewModel.setIsEditingText(true)
    }

    binding.llAddNewText.setPreventDoubleClick {
        if (viewModel.listStickerStateFlow.value.size < 50) {
            val bottomSheetAddText =
                TextOverlayBottomSheet.newInstance(TextOverlayBottomSheetType.CreateNew)
            bottomSheetAddText.setListener(this)
            bottomSheetAddText.show(childFragmentManager)
        } else {
            displayToast(R.string.something_error)
        }
    }
}

fun EditMediaPhotoFragment.backEvent() {
    activity?.onBackPressedDispatcher?.onBackPressed()
}

fun EditMediaPhotoFragment.setupToolbar() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun EditMediaPhotoFragment.setupButtons() {
    binding.llShare.setPreventDoubleClickScaleView {
        viewModel.sharePhoto(binding.photoTextOverlayView.captureAsBitmap())
    }

    binding.llDelete.setPreventDoubleClickScaleView {
        viewModel.deletePhoto(
            onSuccess = {
                launchMain {
                    displayToast(getString(R.string.photo_deleted_successfully))
                    backEvent()
                }
            },
            onError = {
                launchMain {
                    displayToast(getString(R.string.photo_delete_failed))
                }
            }
        )
    }

    binding.llSaveAs.setPreventDoubleClickScaleView {
        val bitmap = binding.photoTextOverlayView.captureAsBitmap()
        viewModel.saveImageToDCIMThenUpdateMediaItem(
            bitmap = bitmap,
            onSuccess = {
                launchMain {
                    requireContext().showOkDialog(getString(R.string.photo_saved_successfully)) {
                        backEvent()
                    }
                }
            },
            onError = {
                launchMain {
                    displayToast(getString(R.string.photo_save_failed))
                }
            }
        )
    }

    binding.btnCancel.setPreventDoubleClickScaleView {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.cancel_editing))
            .setMessage(getString(R.string.cancel_editing_message))
            .setPositiveButton(getString(R.string.yes)) { dialog, _ ->
                viewModel.removeAllSticker()
                viewModel.setIsEditingText(false)
                dialog.dismiss()
            }
            .setNegativeButton(getString(R.string.no)) { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }

    binding.btnSave.setPreventDoubleClickScaleView {
        saveBitmapWithTextEvent()
    }
}


fun EditMediaPhotoFragment.saveBitmapWithTextEvent() {
    launchIO {
        val listSticker = viewModel.listStickerStateFlow.value.onEach { it.stickerV.setControlItemsHidden(true) }
        viewModel.setListSticker(listSticker)

        val bitmap = binding.photoTextOverlayView.captureAsBitmap()
        val imageFinal = addAllStickers(bitmap, viewModel.listStickerStateFlow.value)

        withContext(Dispatchers.Main) {
            binding.photoTextOverlayView.setImageBitmap(imageFinal)
            viewModel.removeAllSticker()
            viewModel.setIsEditingText(false)
        }
    }
}

fun EditMediaPhotoFragment.loadImage(cameraGPSSavedMediaItem: CameraGPSSavedMediaItem) {
    Glide.with(requireContext())
        .asBitmap()
        .load(cameraGPSSavedMediaItem.uri)
        .into(object : CustomTarget<Bitmap>() {
            override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                binding.photoTextOverlayView.post {
                    binding.photoTextOverlayView.setImageBitmap(
                        BitmapUtils.resizeBitmapToFitView(
                            resource,
                            binding.photoTextOverlayView.width,
                            binding.photoTextOverlayView.height
                        )
                    )
                }
            }
            override fun onLoadCleared(placeholder: Drawable?) {}
        })

    // Set title
    binding.tvLocationName.text = cameraGPSSavedMediaItem.locationName
    binding.tvTimestamp.text =
        android.text.format.DateFormat.format("dd/MM/yyyy HH:mm", cameraGPSSavedMediaItem.dateCreated)
}

fun EditMediaPhotoFragment.observeUiState() {
    viewLifecycleOwner.lifecycleScope.launch {
        viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
            viewModel.uiState.collectLatest { state ->
                // Update loading state
                binding.progressBar.visibility =
                    if (state.isLoading) View.VISIBLE else View.GONE

                // Update text editing state
                updateTextEditingUI(state.isEditingText)

                // Show error if any
                state.error?.let { error ->
                    displayToast(error)
                    viewModel.clearError()
                }
            }
        }
    }
}

fun EditMediaPhotoFragment.updateTextEditingUI(isEditing: Boolean) {
    binding.toolbarDefault.visibility = if (isEditing) View.GONE else View.VISIBLE
    binding.toolbarTextEdit.visibility = if (isEditing) View.VISIBLE else View.GONE
    binding.llAddNewText.visibility = if (isEditing) View.VISIBLE else View.GONE
    binding.bottomActions.visibility = if (isEditing) View.INVISIBLE else View.VISIBLE
}

fun EditMediaPhotoFragment.addAllStickers(
    background: Bitmap,
    stickers: List<StickerModel>,
    index: Int = 0
): Bitmap {
    // Nếu đã xử lý hết danh sách sticker, trả về ảnh cuối cùng
    if (index >= stickers.size) return background

    // Thêm sticker hiện tại vào ảnh
    val updatedBitmap = if (stickers[index].stickerV is StickerImageView) addStickerOnImage(
        background,
        stickers[index]
    ) else
        addStickerTvOnImage(background, stickers[index])

    // Gọi đệ quy để xử lý sticker tiếp theo
    return addAllStickers(updatedBitmap, stickers, index + 1)
}

fun EditMediaPhotoFragment.addStickerTvOnImage(
    background: Bitmap,
    stickerModel: StickerModel
): Bitmap {
    val result = createBitmap(background.width, background.height)
    val canvas = Canvas(result)
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    val tattooBitmap = getBitmapFromView(stickerModel.stickerV)

    // Vẽ ảnh lớn lên canvas
    canvas.drawBitmap(
        background,
        0f,
        0f,
        null
    )

    //trans
    val tattooPosX = stickerModel.stickerV.x
    val tattooPosY = stickerModel.stickerV.y

    val currentWidth = stickerModel.stickerV.width - convertDpToPx(StickerView.BUTTON_SIZE_DP)

    val resultTattoo =
        createBitmap(tattooBitmap.width, tattooBitmap.height)
            .apply {
                val canvasTattoo = Canvas(this)
                val paintTattoo = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                    alpha = (stickerModel.opacity / 100 * 255)
                }

                canvasTattoo.drawBitmap(tattooBitmap, 0f, 0f, paintTattoo)
            }

    //center
    val centerX =
        stickerModel.stickerV.x + convertDpToPx(StickerView.BUTTON_SIZE_DP / 2) + currentWidth / 2
    val centerY =
        stickerModel.stickerV.y + convertDpToPx(StickerView.BUTTON_SIZE_DP / 2) + currentWidth / 2

    // Vẽ ảnh nhỏ lên vị trí mong muốn
    val matrix = Matrix()

    matrix.setTranslate(tattooPosX, tattooPosY)
    matrix.postRotate(stickerModel.stickerV.rotation, centerX, centerY)

    canvas.drawBitmap(resultTattoo, matrix, paint)

    return result
}

fun EditMediaPhotoFragment.getBitmapFromView(view: View): Bitmap {
    val bitmap = createBitmap(view.width, view.height)
    val canvas = Canvas(bitmap)
    view.draw(canvas)
    return bitmap
}

fun EditMediaPhotoFragment.addStickerOnImage(
    background: Bitmap,
    stickerModel: StickerModel
): Bitmap {
    val result = createBitmap(background.width, background.height)
    val canvas = Canvas(result)
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)

    val tattooBitmap = getBitmapFromView(stickerModel.stickerV)

    // Vẽ ảnh lớn lên canvas
    canvas.drawBitmap(
        background,
        0f,
        0f,
        null
    )

    //trans
    val tattooPosX = stickerModel.stickerV.x
    val tattooPosY = stickerModel.stickerV.y

    //scale
    val currentWidth = stickerModel.stickerV.width - convertDpToPx(StickerView.BUTTON_SIZE_DP)

    val resultTattoo =
        createBitmap(tattooBitmap.width, tattooBitmap.height)
            .apply {
                val canvasTattoo = Canvas(this)
                val paintTattoo = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                    alpha = (stickerModel.opacity / 100 * 255)
                }

                canvasTattoo.drawBitmap(tattooBitmap, 0f, 0f, paintTattoo)
            }

    //center
    val centerX =
        stickerModel.stickerV.x + convertDpToPx(StickerView.BUTTON_SIZE_DP / 2) + currentWidth / 2
    val centerY =
        stickerModel.stickerV.y + convertDpToPx(StickerView.BUTTON_SIZE_DP / 2) + currentWidth / 2

    // Vẽ ảnh nhỏ lên vị trí mong muốn
    val matrix = Matrix()

    matrix.setTranslate(tattooPosX, tattooPosY)
    matrix.postRotate(stickerModel.stickerV.rotation, centerX, centerY)

    canvas.drawBitmap(resultTattoo, matrix, paint)

    return result
}