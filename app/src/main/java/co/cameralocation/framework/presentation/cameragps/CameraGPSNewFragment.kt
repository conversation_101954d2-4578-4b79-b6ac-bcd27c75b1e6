package co.cameralocation.framework.presentation.cameragps

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.cameralocation.databinding.FragmentCameraGpsNewBinding
import co.cameralocation.framework.presentation.common.BaseFragment

@AndroidEntryPoint
class CameraGPSNewFragment : BaseFragment<FragmentCameraGpsNewBinding, CameraGPSNewViewModel>(
    FragmentCameraGpsNewBinding::inflate,
    CameraGPSNewViewModel::class.java
) {
    override fun init(view: View) {
        setupHomeButton()
        setupCameraGpsButton()
        setupGridPhotoButton()
        setupSaveMediaButton()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // No observers needed for now
    }

    companion object {
        const val TAG = "CameraGPSNewFragment"
    }
}
