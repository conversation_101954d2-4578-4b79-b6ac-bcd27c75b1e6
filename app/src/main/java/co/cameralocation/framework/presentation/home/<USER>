package co.cameralocation.framework.presentation.home

import android.app.Activity
import android.content.Intent
import android.view.View
import co.cameralocation.databinding.FragmentHomeBinding
import co.cameralocation.framework.manager.PermissionManager
import co.cameralocation.framework.presentation.common.BaseFragment
import co.cameralocation.framework.presentation.common.CommonViewModel.GetVideLiveUiState
import co.cameralocation.util.REQUEST_CHECK_SETTINGS
import co.cameralocation.util.displayToast
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class HomeFragment :
    BaseFragment<FragmentHomeBinding, HomeViewModel>(
        FragmentHomeBinding::inflate,
        HomeViewModel::class.java,
    ) {
    val permissionManager: PermissionManager by lazy {
        PermissionManager(requireContext())
    }

    override fun init(view: View) {
        if (commonViewModel.getVideoLiveUiState.value !is GetVideLiveUiState.Success) {
            commonViewModel.getVideoLive()
        }
        initButtonData()
        setLiveVideoEvent()
        setCompassEvent()
        setCameraGPSEvent()
        setIapEvent()
        setSettingsEvent()
        onBackEvent()
        showAds()
        initAppResumeAds()
        iapEvent()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    // Handle the result from the GPS settings dialog
    override fun onActivityResult(
        requestCode: Int,
        resultCode: Int,
        data: Intent?,
    ) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CHECK_SETTINGS) {
            when (resultCode) {
                Activity.RESULT_OK -> {
                    // GPS is enabled, proceed with navigation
                    Timber.d("GPS enabled by user")
                }
                Activity.RESULT_CANCELED -> {
                    // User chose not to enable GPS
                    Timber.d("GPS not enabled by user")
                    displayToast(getString(co.cameralocation.R.string.gps_disabled))
                }
            }
        }
    }

    companion object {
        const val TAG = "HomeFragment"
    }
}
