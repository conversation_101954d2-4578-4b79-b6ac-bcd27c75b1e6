package co.cameralocation.framework.presentation.onboarding.viewpager.onboardNativeFull2

import android.view.View
import co.cameralocation.databinding.FragmentOnboardNativeFullBinding
import co.cameralocation.framework.presentation.common.BaseFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnboardNativeFull2Fragment :
    BaseFragment<FragmentOnboardNativeFullBinding, OnboardNativeFull2ViewModel>(
        FragmentOnboardNativeFullBinding::inflate,
        OnboardNativeFull2ViewModel::class.java,
    ) {

    var isClickAds = false
    var isShowReloadAds = false

    override fun init(view: View) {
        onBackEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        // TODO("Not yet implemented")
    }

    override fun onResume() {
        super.onResume()
        showReloadAds()
    }
}
