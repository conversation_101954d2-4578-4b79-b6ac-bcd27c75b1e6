package co.cameralocation.framework.presentation.gridphoto.adapter

import android.util.TypedValue
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.cameralocation.R
import co.cameralocation.databinding.ItemGridPhotoTabContentBinding
import co.cameralocation.framework.presentation.gridphoto.GridPhotoFragment
import co.cameralocation.framework.presentation.model.gridphoto.GridLayout
import co.cameralocation.framework.presentation.model.gridphoto.GridPhotoTab
import co.cameralocation.util.adapter.GridSpacingItemDecoration
import pion.datlt.libads.utils.adsuntils.showLoadedInter

class GridPhotoViewPagerAdapter(
    private val tabs: Array<GridPhotoTab>,
    private val onGridLayoutClick: (GridLayout) -> Unit,
    private val fragment: GridPhotoFragment
) : RecyclerView.Adapter<GridPhotoViewPagerAdapter.GridPhotoViewHolder>() {
    private var layoutsMap: Map<GridPhotoTab, List<GridLayout>> = emptyMap()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridPhotoViewHolder {
        val binding = ItemGridPhotoTabContentBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return GridPhotoViewHolder(binding)
    }

    override fun onBindViewHolder(holder: GridPhotoViewHolder, position: Int) {
        val tab = tabs[position]
        val layouts = layoutsMap[tab] ?: emptyList()
        holder.bind(layouts)
    }

    override fun getItemCount(): Int = tabs.size

    fun updateLayouts(newLayoutsMap: Map<GridPhotoTab, List<GridLayout>>) {
        this.layoutsMap = newLayoutsMap
        notifyDataSetChanged()
    }

    inner class GridPhotoViewHolder(
        private val binding: ItemGridPhotoTabContentBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private val gridLayoutAdapter = GridLayoutAdapter().apply {
            onItemClick = { layout ->
                fragment.showLoadedInter(
                    spaceNameConfig = "gridlist-chooselayout",
                    spaceName = "grid-1ID_interstitial",
                    destinationToShowAds = R.id.gridPhotoFragment,
                    isShowLoadingView = true,
                    isScreenType = false,
                    navOrBack = {
                        onGridLayoutClick(layout)
                    },
                    onCloseAds = {})
            }
        }


        init {
            binding.rvGridLayouts.apply {
                layoutManager = GridLayoutManager(itemView.context, 2)
                adapter = gridLayoutAdapter

                val spacingInPixels = TypedValue.applyDimension(
                    TypedValue.COMPLEX_UNIT_DIP, 8f, resources.displayMetrics
                ).toInt()

                addItemDecoration(
                    GridSpacingItemDecoration(
                        spanCount = 2,
                        spacing = spacingInPixels,
                        includeEdge = true
                    )
                )
            }
        }

        fun bind(layouts: List<GridLayout>) {
            gridLayoutAdapter.submitList(layouts)
        }
    }
}
