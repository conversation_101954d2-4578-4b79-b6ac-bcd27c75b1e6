package co.cameralocation.framework.presentation.gridphoto

import android.view.View
import dagger.hilt.android.AndroidEntryPoint
import co.cameralocation.databinding.FragmentGridPhotoBinding
import co.cameralocation.framework.presentation.common.BaseFragment

@AndroidEntryPoint
class GridPhotoFragment : BaseFragment<FragmentGridPhotoBinding, GridPhotoViewModel>(
    FragmentGridPhotoBinding::inflate,
    GridPhotoViewModel::class.java
) {
    override fun init(view: View) {
        setupBackButton()
        setupTabs()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeUiState()
        observeUiEvent()
    }

    companion object {
        const val TAG = "GridPhotoFragment"
    }
}
