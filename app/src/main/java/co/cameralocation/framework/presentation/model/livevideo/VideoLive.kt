package co.cameralocation.framework.presentation.model.livevideo

import android.os.Parcelable
import co.cameralocation.util.Constant
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class VideoLive(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("linkVideo")
    val linkVideo: String,
    @SerializedName("latitude")
    val latitude: Double,
    @SerializedName("longitude")
    val longitude: Double,
) : Parcelable

fun VideoLive.toVideoLiveLocal(): VideoLiveLocal {
    return VideoLiveLocal(
        id = this.id,
        name = this.name,
        linkVideo = this.linkVideo,
        latitude = this.latitude,
        longitude = this.longitude,
        likeCount = (100..999).random(),
        watchCount = (10000..500000).random(),
        isPro = Constant.listVideoLiveIdHot.contains(this.id.toString())
    )
}
