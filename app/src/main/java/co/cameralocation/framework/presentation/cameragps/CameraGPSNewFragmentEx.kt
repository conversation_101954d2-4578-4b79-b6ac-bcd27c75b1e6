package co.cameralocation.framework.presentation.cameragps

import androidx.navigation.fragment.findNavController
import co.cameralocation.R
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.util.setPreventDoubleClickScaleView
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun CameraGPSNewFragment.setupHomeButton() {
    binding.btnHome.setPreventDoubleClick {
        showLoadedInter(
            spaceNameConfig = "gps-home",
            spaceName = "gps-1ID_interstitial",
            destinationToShowAds = R.id.cameraGpsNewFragment,
            isShowLoadingView = true,
            isScreenType = false,
            navOrBack = {
                runCatching {
                    findNavController().popBackStack(R.id.homeFragment, false)
                }
            },
            onCloseAds = {},
        )
    }
}

fun CameraGPSNewFragment.showInterAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "gps-function",
        spaceName = "gps-1ID_interstitial",
        destinationToShowAds = R.id.cameraGpsNewFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {
        },
    )
}

fun CameraGPSNewFragment.setupCameraGpsButton() {
    binding.btnCameraGps.setPreventDoubleClickScaleView(
        action = {
            showInterAndDoAction {
                safeNavInter(
                    R.id.cameraGpsNewFragment,
                    R.id.action_cameraGpsNewFragment_to_cameraFragment,
                )
            }
        },
        onTouchDown = {
            binding.btnCameraGps.setBackgroundResource(R.drawable.bg_button_camera_gps)
        },
        onTouchUp = {
            binding.btnCameraGps.setBackgroundResource(R.drawable.bg_button_grid_photo)
        },
    )
}

fun CameraGPSNewFragment.setupGridPhotoButton() {
    binding.btnGridPhoto.setPreventDoubleClickScaleView(
        action = {
            showInterAndDoAction {
                safeNavInter(
                    R.id.cameraGpsNewFragment,
                    R.id.action_cameraGpsNewFragment_to_gridPhotoFragment,
                )
            }
        },
        onTouchDown = {
            binding.btnGridPhoto.setBackgroundResource(R.drawable.bg_button_camera_gps)
        },
        onTouchUp = {
            binding.btnGridPhoto.setBackgroundResource(R.drawable.bg_button_grid_photo)
        },
    )
}

fun CameraGPSNewFragment.setupSaveMediaButton() {
    binding.btnSaveMedia.setPreventDoubleClickScaleView(
        action = {
            showInterAndDoAction {
                safeNavInter(
                    R.id.cameraGpsNewFragment,
                    R.id.action_cameraGpsNewFragment_to_savePhotoFragment,
                )
            }
        },
        onTouchDown = {
            binding.btnSaveMedia.setBackgroundResource(R.drawable.bg_button_camera_gps)
        },
        onTouchUp = {
            binding.btnSaveMedia.setBackgroundResource(R.drawable.bg_button_grid_photo)
        },
    )
}

fun CameraGPSNewFragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "gps1",
        spaceName = "gps1_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )

    showLoadedNative(
        spaceNameConfig = "gps2",
        spaceName = "gps2_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )

    safePreloadAds(
        listSpaceNameConfig = listOf("gps-function", "gps-home"),
        spaceNameAds = "gps-1ID_interstitial",
    )
}
