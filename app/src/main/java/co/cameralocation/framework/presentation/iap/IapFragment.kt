package co.cameralocation.framework.presentation.iap

import android.os.Bundle
import android.view.View
import co.cameralocation.databinding.FragmentIapBinding
import co.cameralocation.framework.presentation.common.BaseFragment
import co.cameralocation.util.BundleKey
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class IapFragment :
    BaseFragment<FragmentIapBinding, IapViewModel>(
        FragmentIapBinding::inflate,
        IapViewModel::class.java,
    ) {
    var isOnboard: Boolean = false

    override fun init(view: View) {
        initView()
        skipEvent()
        useAdsVersionEvent()
        subscribeEvent()
        onBackEvent()
    }

    override fun subscribeObserver(view: View) {
        // Nothing to observe yet
    }

    companion object {
        fun newInstance(isOnboard: Boolean) =
            IapFragment().apply {
                arguments =
                    Bundle().apply {
                        putBoolean(BundleKey.KEY_ONBOARD_IAP, isOnboard)
                    }
            }
    }
}
