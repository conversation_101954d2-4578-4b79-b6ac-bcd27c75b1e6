package co.cameralocation.framework.presentation.gridphoto

import android.util.Log
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.viewpager2.widget.ViewPager2
import co.cameralocation.R
import co.cameralocation.framework.presentation.common.onSystemBackEvent
import co.cameralocation.framework.presentation.gridphoto.GridPhotoFragment.Companion.TAG
import co.cameralocation.framework.presentation.gridphoto.adapter.GridPhotoViewPagerAdapter
import co.cameralocation.framework.presentation.model.gridphoto.GridLayout
import co.cameralocation.framework.presentation.model.gridphoto.GridPhotoTab
import co.cameralocation.util.Constant
import co.cameralocation.util.collectFlowOnView
import co.cameralocation.util.setPreventDoubleClick
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayout.OnTabSelectedListener
import com.google.android.material.tabs.TabLayoutMediator
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import kotlin.collections.get

fun GridPhotoFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
    onSystemBackEvent {
        backEvent()
    }
}

fun GridPhotoFragment.backEvent() {
    Log.d(TAG, "backEvent: ")
    showLoadedInter(
        spaceNameConfig = "gridlist-back",
        spaceName = "grid-1ID_interstitial",
        destinationToShowAds = R.id.gridPhotoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            runCatching {
                findNavController().navigateUp()
            }
        },
        onCloseAds = {},
    )
}

fun GridPhotoFragment.setupTabs() {
    val tabLayout = binding.tabLayout
    val viewPager = binding.viewPager

    // Create adapter with empty layouts map initially
    val viewPagerAdapter =
        GridPhotoViewPagerAdapter(
            tabs = GridPhotoTab.entries.toTypedArray(),
            onGridLayoutClick = { layout ->
                viewModel.selectGridLayout(layout)
            },
            fragment = this,
        )

    viewPager.adapter = viewPagerAdapter
    viewPager.isUserInputEnabled = false

    TabLayoutMediator(tabLayout, viewPager) { tab, position ->
        tab.text = requireContext().getString(GridPhotoTab.entries[position].titleResId)
    }.attach()
    var isFirstTabSelected = true
    var pendingTabPosition = 0
    // Add tab selected listener
    tabLayout.addOnTabSelectedListener(
        object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                val selectedPosition = tab?.position ?: 0
                Log.d("asgawgawgawgagwgwa", "onTabSelected: chay vao day 1")
                // If it's the initial selection or same tab, don't show ad
                if (isFirstTabSelected || selectedPosition == viewPager.currentItem) {
                    isFirstTabSelected = false
                    viewPager.currentItem = selectedPosition
                    viewModel.selectTab(GridPhotoTab.entries[selectedPosition])
                    return
                }

                // Store the pending position
                pendingTabPosition = selectedPosition
                Log.d("asgawgawgawgagwgwa", "onTabSelected: chay vao day 2")

                // Show ad and then change ViewPager page when ad is closed
                showLoadedInter(
                    spaceNameConfig = "gridlist-choosecate",
                    spaceName = "grid-1ID_interstitial",
                    destinationToShowAds = R.id.gridPhotoFragment,
                    isShowLoadingView = true,
                    timeShowLoadingView = 0,
                    isScreenType = false,
                    navOrBack = {
                        // Update ViewPager after ad is closed
                        viewPager.currentItem = pendingTabPosition
                        viewModel.selectTab(GridPhotoTab.entries[pendingTabPosition])
                    },
                    onCloseAds = {
                    },
                )
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // Not needed
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // Handle tab reselection if needed
            }
        },
    )

    viewPager.registerOnPageChangeCallback(
        object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                viewModel.selectTab(GridPhotoTab.entries[position])
            }
        },
    )

    // Update adapter when layouts change
    viewModel.uiState
        .map { it.gridLayouts }
        .distinctUntilChanged()
        .collectFlowOnView(viewLifecycleOwner, Lifecycle.State.RESUMED) { layoutsMap ->
            viewPagerAdapter.updateLayouts(layoutsMap)
        }
}

fun GridPhotoFragment.observeUiState() {
    viewModel.uiState
        .onEach { state ->
            showTabContent()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun GridPhotoFragment.observeUiEvent() {
    viewModel.uiEvent
        .onEach { event ->
            when (event) {
                is GridPhotoViewModel.GridPhotoUiEvent.NavigateToPickPhoto -> {
                    navigateToPickPhoto(event.selectedLayout)
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun GridPhotoFragment.showTabContent() {
    binding.apply {
        viewPager.isVisible = true
        tabLayout.isVisible = true
        rvOrientations.isVisible = false
        tvOrientationTitle.isVisible = false
    }
}

fun GridPhotoFragment.navigateToPickPhoto(gridLayout: GridLayout) {
    // Create bundle with grid layout and orientation
    val bundle =
        bundleOf(
            Constant.KEY_GRID_LAYOUT to gridLayout,
        )

    safeNavInter(R.id.gridPhotoFragment, R.id.action_gridPhotoFragment_to_pickPhotoFragment, bundle)
}

fun GridPhotoFragment.showAds() {
    if (AdsConstant.listConfigAds["gridlist1"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
        showLoadedNative(
            spaceNameConfig = "gridlist1",
            spaceName = "gridlist1_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
    if (AdsConstant.listConfigAds["gridlist1"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER_ADAPTIVE) {
        showLoadedBannerAdaptive(
            spaceNameConfig = "gridlist1",
            spaceName = "gridlist1_adaptive",
            ratioView = "360:70",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }
    safePreloadAds(
        listSpaceNameConfig =
            listOf(
                "gridlist-back",
                "gridlist-choosecate",
                "gridlist-chooselayout",
            ),
        spaceNameAds = "grid-1ID_interstitial",
    )
}
