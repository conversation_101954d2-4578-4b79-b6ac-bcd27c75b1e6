package co.cameralocation.framework.presentation.pickphoto

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.view.View
import android.widget.AdapterView
import android.widget.ArrayAdapter
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import co.cameralocation.R
import co.cameralocation.framework.presentation.common.onSystemBackEvent
import co.cameralocation.framework.presentation.model.gridphoto.GridLayout
import co.cameralocation.framework.presentation.model.pickphoto.DevicePhoto
import co.cameralocation.framework.presentation.model.pickphoto.PhotoSourceMode
import co.cameralocation.framework.presentation.pickphoto.adapter.DevicePhotoAdapter
import co.cameralocation.framework.presentation.pickphoto.adapter.SelectedDevicePhoto
import co.cameralocation.framework.presentation.pickphoto.adapter.SelectedPhotoAdapter
import co.cameralocation.util.Constant
import co.cameralocation.util.parcelable
import co.cameralocation.util.setPreventDoubleClick
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun PickPhotoFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
    onSystemBackEvent {
        backEvent()
    }
}

fun PickPhotoFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "grid-selectpt-back",
        spaceName = "grid-1ID_interstitial",
        destinationToShowAds = R.id.pickPhotoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            runCatching {
                findNavController().navigateUp()
            }
        },
        onCloseAds = {},
    )
}

fun PickPhotoFragment.setupGridLayout() {
    val gridLayout = arguments?.parcelable<GridLayout>(Constant.KEY_GRID_LAYOUT)

    if (gridLayout != null) {
        viewModel.setGridLayout(gridLayout)
    }
}

fun PickPhotoFragment.setupDevicePhotoAdapter() {
    devicePhotoAdapter =
        DevicePhotoAdapter().apply {
            onItemClick = { photo ->
                viewModel.togglePhotoSelection(photo)
            }
        }

    binding.rvDevicePhotos.apply {
        adapter = devicePhotoAdapter
        layoutManager = GridLayoutManager(requireContext(), 3)
    }
}

fun PickPhotoFragment.setupSelectedPhotoAdapter() {
    selectedPhotoAdapter =
        SelectedPhotoAdapter().apply {
            onRemoveClick = { photo ->
                viewModel.removeSelectedPhoto(photo)
            }
        }

    binding.rvSelectedPhotos.apply {
        adapter = selectedPhotoAdapter
        layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
    }
}

fun PickPhotoFragment.setupButtons() {
    binding.btnClearAll.setPreventDoubleClick {
        viewModel.clearAllSelectedPhotos()
    }

    binding.btnNext.setPreventDoubleClick {
        showLoadedInter(
            spaceNameConfig = "grid-selectpt-continue",
            spaceName = "grid-1ID_interstitial",
            destinationToShowAds = R.id.pickPhotoFragment,
            isShowLoadingView = true,
            isScreenType = false,
            navOrBack = {
                viewModel.navigateToEditGridPhoto()
            },
            onCloseAds = {},
        )
    }
}

fun PickPhotoFragment.checkPermissionsAndLoadPhotos() {
    val permissions =
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arrayOf(Manifest.permission.READ_MEDIA_IMAGES)
        } else {
            arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE)
        }

    if (permissions.all {
            ContextCompat.checkSelfPermission(
                requireContext(),
                it,
            ) == PackageManager.PERMISSION_GRANTED
        }
    ) {
        loadPhotos()
    } else {
        requestPermissionLauncher.launch(permissions)
    }
}

fun PickPhotoFragment.setupPhotoSourceSpinner() {
    val photoSources =
        arrayOf(
            getString(R.string.all_photos),
            getString(R.string.in_app),
        )

    val adapter = ArrayAdapter(requireContext(), R.layout.item_photo_source_spinner, photoSources)
    adapter.setDropDownViewResource(R.layout.item_photo_source_spinner)

    binding.spinnerPhotoSource.adapter = adapter
    binding.spinnerPhotoSource.onItemSelectedListener =
        object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(
                parent: AdapterView<*>?,
                view: View?,
                position: Int,
                id: Long,
            ) {
                val mode =
                    when (position) {
                        0 -> PhotoSourceMode.ALL_PHOTOS
                        1 -> PhotoSourceMode.IN_APP
                        else -> PhotoSourceMode.ALL_PHOTOS
                    }
                viewModel.setPhotoSourceMode(mode)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {
                // Do nothing
            }
        }
}

fun PickPhotoFragment.loadPhotos() {
    viewModel.loadPhotos()
}

fun PickPhotoFragment.observeDevicePhotoListChanged() {
    // Observe photo source mode changes
    viewModel.uiState
        .map {
            Pair(
                it.photoSourceMode,
                if (it.photoSourceMode == PhotoSourceMode.ALL_PHOTOS) it.devicePhotos else it.appPhotos,
            )
        }.distinctUntilChanged()
        .onEach { (mode, photos) ->
            devicePhotoAdapter.submitList(photos)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observeSelectedPhotoListChanged() {
    viewModel.uiState
        .map { it.selectedPhotos }
        .distinctUntilChanged()
        .onEach { selectedPhotos ->
            selectedPhotoAdapter.submitList(createListSelectedPhotos(selectedPhotos))
            showAdsNativeFullPhoto(selectedPhotos)
            binding.tvRequiredPhotoCount.text =
                getString(
                    R.string.required_photo_count,
                    selectedPhotos.size,
                    viewModel.uiState.value.requiredPhotoCount,
                )
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observeNextButtonState() {
    viewModel.uiState
        .map { it.isEnableGoToNextScreen }
        .distinctUntilChanged()
        .onEach { isEnable ->
            binding.btnNext.isEnabled = isEnable
            binding.btnNext.alpha = if (isEnable) 1f else 0.3f
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observeLoadingState() {
    viewModel.uiState
        .map { it.isLoading }
        .distinctUntilChanged()
        .onEach { isLoading ->
            binding.progressLoading.isVisible = isLoading
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.createListSelectedPhotos(selectedPhotos: List<DevicePhoto>): List<SelectedDevicePhoto> =
    buildList {
        selectedPhotos.forEach {
            add(SelectedDevicePhoto.Photo(it))
        }
        val emptySlots = viewModel.uiState.value.requiredPhotoCount - selectedPhotos.size
        repeat(emptySlots) {
            add(SelectedDevicePhoto.Empty)
        }
    }

fun PickPhotoFragment.showAdsNativeFullPhoto(selectedPhotos: List<DevicePhoto>) {
    val emptySlots = viewModel.uiState.value.requiredPhotoCount - selectedPhotos.size
    if (emptySlots == 0 && !isShowingNative2) {
        isShowingNative2 = true
        showLoadedNative(
            spaceNameConfig = "grid-selectpt2",
            spaceName = "grid-selectpt2_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
}

fun PickPhotoFragment.observePhotoSourceMode() {
    viewModel.uiState
        .map { it.photoSourceMode }
        .distinctUntilChanged()
        .onEach { mode ->
            binding.spinnerPhotoSource.setSelection(
                when (mode) {
                    PhotoSourceMode.ALL_PHOTOS -> 0
                    PhotoSourceMode.IN_APP -> 1
                },
            )
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.observeUiEvent() {
    viewModel.uiEvent
        .onEach { event ->
            when (event) {
                is PickPhotoViewModel.PickPhotoUiEvent.NavigateToEditGridPhoto -> {
                    navigateToEditGridPhoto(event.selectedPhotos)
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun PickPhotoFragment.navigateToEditGridPhoto(selectedPhotos: List<DevicePhoto>) {
    val gridLayout = arguments?.parcelable<GridLayout>(Constant.KEY_GRID_LAYOUT)

    val bundle =
        bundleOf(
            Constant.KEY_GRID_LAYOUT to gridLayout,
            Constant.KEY_SELECTED_PHOTOS to selectedPhotos,
        )

    safeNavInter(
        R.id.pickPhotoFragment,
        R.id.action_pickPhotoFragment_to_editGridPhotoFragment,
        bundle,
    )
}

fun PickPhotoFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("grid-selectpt-back", "grid-selectpt-continue"),
        spaceNameAds = "grid-1ID_interstitial",
    )

    val emptySlots =
        viewModel.uiState.value.requiredPhotoCount - viewModel.uiState.value.selectedPhotos.size
    if (emptySlots > 0) {
        if (AdsConstant.listConfigAds["grid-selectpt1"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
            showLoadedNative(
                spaceNameConfig = "grid-selectpt1",
                spaceName = "grid-selectpt1_native",
                layoutToAttachAds = binding.adViewGroup,
                layoutContainAds = binding.layoutAds,
            )
        }
        if (AdsConstant.listConfigAds["grid-selectpt1"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER_ADAPTIVE) {
            showLoadedBannerAdaptive(
                spaceNameConfig = "grid-selectpt1",
                spaceName = "grid-selectpt1_adaptive",
                ratioView = "360:70",
                layoutToAttachAds = binding.adViewGroup,
                layoutContainAds = binding.layoutAds,
            )
        }
    }
}
