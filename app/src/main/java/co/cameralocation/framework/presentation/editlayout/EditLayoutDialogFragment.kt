package co.cameralocation.framework.presentation.editlayout

import android.content.DialogInterface
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.KeyEvent
import androidx.fragment.app.viewModels
import co.cameralocation.R
import co.cameralocation.databinding.FragmentEditLayoutBinding
import co.cameralocation.framework.presentation.common.BaseDialogFragment
import co.cameralocation.framework.presentation.editlayout.adapter.LayoutSettingAdapter
import co.cameralocation.framework.presentation.editlocation.adapter.LayoutItemData
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class EditLayoutDialogFragment(
    val itemData: LayoutItemData? = null,
    val onUpdateDefaultTitle: (String) -> Unit,
    val onUpdateShowTitle: (Boolean) -> Unit,
    val onUpdateShowLocation: (Boolean) -> Unit,
    val onUpdateShowCoordinates: (Boolean) -> Unit,
    val onUpdateShowDateTime: (Boolean) -> Unit,
    val onUpdateShowWeather: (Boolean) -> Unit,
    val onCloseDialog: (hasUpdateData: Boolean) -> Unit,
) : BaseDialogFragment<FragmentEditLayoutBinding>(R.layout.fragment_edit_layout) {
    lateinit var layoutSettingAdapter: LayoutSettingAdapter

    val viewModel: EditLayoutViewModel by viewModels()
    var hasUpdateData = false

    fun setUpDialogSize() {
        val displayMetrics = DisplayMetrics()
        dialog
            ?.window
            ?.windowManager
            ?.defaultDisplay
            ?.getMetrics(displayMetrics)
        // Lấy chiều rộng và chiều cao màn hình
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        // Tính toán chiều rộng và chiều cao mới theo tỷ lệ phần trăm
        val desiredWidth = screenWidth // 100% chiều rộng
        val desiredHeight = screenHeight // 100% chiều cao
        // Đặt LayoutParams cho cửa sổ dialog
        dialog?.window?.setLayout(desiredWidth, desiredHeight)
        dialog?.setCancelable(false)
    }

    override fun onResume() {
        super.onResume()
        dialog?.setOnKeyListener(
            object : DialogInterface.OnKeyListener {
                override fun onKey(
                    dialog: DialogInterface?,
                    keyCode: Int,
                    event: KeyEvent?,
                ): Boolean {
                    if (keyCode == KeyEvent.KEYCODE_BACK && event?.action == KeyEvent.ACTION_UP) {
                        backEvent()
                        return true
                    }
                    return false
                }
            },
        )
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        viewModel.loadDefaultUnknownLocationTitleGlobally(itemData?.defaultTitle ?: "")
        observeTitle()
        observeShowTitle()
        observeShowLocation()
        observeShowCoordinates()
        observeShowDateTime()
        observeShowWeather()
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setUpDialogSize()
        setupBackButton()
        setupRecyclerView()
        showAds()
    }

    companion object {
        const val TAG = "EditLayoutFragment"

        const val ITEM_ID_TITLE = "title"
        const val ITEM_ID_LOCATION = "location"
        const val ITEM_ID_COORDINATES = "coordinates"
        const val ITEM_ID_DATETIME = "datetime"
        const val ITEM_ID_WEATHER = "weather"
    }
}
