package co.cameralocation.framework.presentation.model.media

import android.graphics.Color
import android.graphics.Typeface
import android.text.Layout

data class EditMediaPhotoUiState(
    val cameraGPSSavedMediaItem: CameraGPSSavedMediaItem? = null,
    val isLoading: Boolean = false,
    val isEditingText: Boolean = false,
    val textOverlayState: TextOverlayState = TextOverlayState(),
    val error: String? = null
)

data class TextOverlayState(
    val texts: List<TextOverlay> = emptyList(),
    val selectedTextIndex: Int = -1
)

data class TextOverlay(
    val id: String = java.util.UUID.randomUUID().toString(),
    val text: String = "",
    val x: Float = 0f,
    val y: Float = 0f,
    val textSize: Float = 60f,
    val textColor: Int = Color.WHITE,
    val textAlignment: Layout.Alignment = Layout.Alignment.ALIGN_CENTER,
    val typeface: Int = Typeface.NORMAL,
    val isUnderlined: Boolean = false
)
