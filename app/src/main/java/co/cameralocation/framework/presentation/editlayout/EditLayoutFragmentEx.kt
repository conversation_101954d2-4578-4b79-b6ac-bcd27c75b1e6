package co.cameralocation.framework.presentation.editlayout

import androidx.recyclerview.widget.LinearLayoutManager
import co.cameralocation.R
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.common.onSystemBackEvent
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_COORDINATES
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_DATETIME
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_LOCATION
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_TITLE
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_WEATHER
import co.cameralocation.framework.presentation.editlayout.adapter.LayoutSettingAdapter
import co.cameralocation.framework.presentation.editlayout.model.LayoutSettingItem
import co.cameralocation.util.setPreventDoubleClick
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun EditLayoutDialogFragment.observeTitle() {
    launchMain {
        viewModel.uiState
            .map { it.defaultUnknownLocationTitle }
            .distinctUntilChanged()
            .collectLatest { titleUiState ->
                layoutSettingAdapter.updateTitleText(titleUiState)
                layoutSettingAdapter.updateSwitchEnabled(
                    ITEM_ID_TITLE,
                    titleUiState.title.isNotEmpty(),
                )
                onUpdateDefaultTitle(titleUiState.title)
            }
    }
}

fun EditLayoutDialogFragment.observeShowCoordinates() {
    launchMain {
        viewModel.uiState
            .map { it.showCoordinates }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_COORDINATES, show)
                onUpdateShowCoordinates(show)
            }
    }
}

fun EditLayoutDialogFragment.observeShowDateTime() {
    launchMain {
        viewModel.uiState
            .map { it.showDateTime }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_DATETIME, show)
                // commonViewModel.updateShowDateTimeVisibilityForLayoutGlobally(show)
                onUpdateShowDateTime(show)
            }
    }
}

fun EditLayoutDialogFragment.observeShowWeather() {
    launchMain {
        viewModel.uiState
            .map { it.showWeather }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_WEATHER, show)
                onUpdateShowWeather(show)
            }
    }
}

fun EditLayoutDialogFragment.observeShowTitle() {
    launchMain {
        viewModel.uiState
            .map { it.showTitle }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_TITLE, show)
                layoutSettingAdapter.updateSwitchEnabled(ITEM_ID_LOCATION, show)
                onUpdateShowTitle(show)
            }
    }
}

fun EditLayoutDialogFragment.observeShowLocation() {
    launchMain {
        viewModel.uiState
            .map { it.showLocation }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_LOCATION, show)
                layoutSettingAdapter.updateSwitchEnabled(ITEM_ID_TITLE, show)
                onUpdateShowLocation(show)
            }
    }
}

fun EditLayoutDialogFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "edit-layout-back",
        spaceName = "camera-1ID_interstitial",
        isShowLoadingView = true,
        destinationToShowAds = R.id.editLocationNewFragment,
        timeShowLoadingView = 0,
        isScreenType = false,
        navOrBack = {
            onCloseDialog(hasUpdateData)
            dismiss()
        },
        onCloseAds = {},
    )
}

fun EditLayoutDialogFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
    onSystemBackEvent {
        backEvent()
    }
}

fun EditLayoutDialogFragment.setupRecyclerView() {
    layoutSettingAdapter =
        LayoutSettingAdapter().apply {
            onSwitchChanged = { item, isChecked ->
                hasUpdateData = true
                Timber.d("LayoutSettingAdapter.onSwitchChanged: ${item.title}, isChecked=$isChecked")
                when (item.id) {
                    ITEM_ID_TITLE -> viewModel.toggleShowTitle(isChecked)
                    ITEM_ID_LOCATION -> viewModel.toggleShowLocation(isChecked)
                    ITEM_ID_COORDINATES -> viewModel.toggleShowCoordinates(isChecked)
                    ITEM_ID_DATETIME -> viewModel.toggleShowDateTime(isChecked)
                    ITEM_ID_WEATHER -> viewModel.toggleShowWeather(isChecked)
                }
            }
            onSaveTitle = { title ->
                hasUpdateData = true
                Timber.d("onSaveTitle: $title")
                viewModel.saveDefaultUnknownLocationTitleGlobally(title)
            }
        }

    binding.settingsRecyclerView.apply {
        layoutManager = LinearLayoutManager(requireContext())
        adapter = layoutSettingAdapter
    }

    // Create initial settings list
    val settings =
        listOf(
            LayoutSettingItem(
                id = ITEM_ID_TITLE,
                title = getString(R.string.show_title),
                description = getString(R.string.edit_title),
                isEnabled = itemData?.isRequestedTitleVisible == true,
                isFixed = false,
                hasEditText = true,
            ),
            LayoutSettingItem(
                id = ITEM_ID_LOCATION,
                title = getString(R.string.show_location),
                description = "",
                isEnabled = itemData?.isLocationVisible == true,
                isFixed = false,
                hasEditText = false,
            ),
            LayoutSettingItem(
                id = ITEM_ID_COORDINATES,
                title = getString(R.string.show_coordinates),
                description = "",
                isEnabled = itemData?.isCoordinatesVisible == true,
                isFixed = false,
                hasEditText = false,
            ),
            LayoutSettingItem(
                id = ITEM_ID_DATETIME,
                title = getString(R.string.show_datetime),
                description = "",
                isEnabled = itemData?.isDateTimeVisible == true,
                isFixed = false,
                hasEditText = false,
            ),
            LayoutSettingItem(
                id = ITEM_ID_WEATHER,
                title = getString(R.string.show_weather),
                description = "",
                isEnabled = itemData?.isWeatherVisible == true,
                isFixed = false,
                hasEditText = false,
            ),
        )

    layoutSettingAdapter.submitList(settings)

    if (itemData?.isRequestedTitleVisible == false) {
        // If title is hidden, cannot hide location
        layoutSettingAdapter.updateSwitchEnabled(ITEM_ID_LOCATION, false)
    }

    if (itemData?.isLocationVisible == false) {
        // If location is hidden, cannot hide title
        layoutSettingAdapter.updateSwitchEnabled(ITEM_ID_TITLE, false)
    }
}

fun EditLayoutDialogFragment.showAds() {
    if (AdsConstant.listConfigAds["edit-layout1"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
        showLoadedNative(
            spaceNameConfig = "edit-layout1",
            spaceName = "edit-layout1_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
    if (AdsConstant.listConfigAds["edit-layout1"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER_ADAPTIVE) {
        showLoadedBannerAdaptive(
            spaceNameConfig = "edit-layout1",
            spaceName = "edit-layout1_adaptive",
            ratioView = "360:70",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }
}
