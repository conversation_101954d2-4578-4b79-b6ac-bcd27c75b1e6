package co.cameralocation.framework.presentation.language.adapter

import co.cameralocation.R
import co.cameralocation.databinding.ItemLanguageBinding
import co.cameralocation.framework.presentation.common.BaseListAdapter
import co.cameralocation.framework.presentation.common.createDiffCallback
import co.cameralocation.framework.presentation.model.language.Language
import co.cameralocation.util.loadImage
import co.cameralocation.util.setPreventDoubleClick

class LanguageAdapter : BaseListAdapter<Language, ItemLanguageBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem },
        getChangePayload = { oldItem, newItem -> getChangePayload(oldItem, newItem) }
    )
) {
    var onItemClick: (Int) -> Unit = {}

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_language

    override fun bindView(
        binding: ItemLanguageBinding,
        item: Language,
        position: Int,
        payloads: MutableList<Any>
    ) {
        if (payloads.isEmpty()) {
            bindView(binding, item, position)
        } else {
            payloads.forEach { payload ->
                when (payload) {
                    PAYLOAD_SELECTION_CHANGED -> updateSelectedState(binding, item)
                }
            }
        }
    }

    override fun bindView(binding: ItemLanguageBinding, item: Language, position: Int) {
        binding.apply {
            // Set language name
            tvLanguageName.text = item.name
            
            // Load flag image
            ivFlag.loadImage(item.flagUrl)
            
            // Show/hide selection indicator
            updateSelectedState(binding, item)
            
            // Set click listener
            root.setPreventDoubleClick {
                onItemClick(position)
            }
        }
    }

    private fun updateSelectedState(binding: ItemLanguageBinding, item: Language) {
        binding.ivSelected.isSelected = item.isSelected
    }

    companion object {
        private const val PAYLOAD_SELECTION_CHANGED = "PAYLOAD_SELECTION_CHANGED"

        fun getChangePayload(oldItem: Language, newItem: Language): Any? {
            if (oldItem.isSelected != newItem.isSelected) {
                return PAYLOAD_SELECTION_CHANGED
            }
            return null
        }
    }
}
