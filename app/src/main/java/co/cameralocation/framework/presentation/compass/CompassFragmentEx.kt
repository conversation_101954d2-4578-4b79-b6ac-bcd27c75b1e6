package co.cameralocation.framework.presentation.compass

import android.hardware.SensorEvent
import androidx.navigation.fragment.findNavController
import co.cameralocation.R
import co.cameralocation.util.setPreventDoubleClickScaleView
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import kotlin.math.roundToInt
import kotlin.math.sqrt

fun CompassFragment.backEvent() {
    binding.btnHome.setPreventDoubleClickScaleView {
        showLoadedInter(
            spaceNameConfig = "compass-home",
            spaceName = "compass-1ID_interstitial",
            destinationToShowAds = R.id.compassFragment,
            isShowLoadingView = true,
            isScreenType = false,
            navOrBack = {
                runCatching {
                    findNavController().navigateUp()
                }
            },
            onCloseAds = {},
        )
    }
}

fun CompassFragment.processSensorOrientationChange(event: SensorEvent) {
    if (!event.values[0].isNaN()) {
        val degreeX = event.values[0].roundToInt()
        val toTalDegreeOrientation =
            sqrt((degreeX * degreeX).toDouble()).toInt()

        commonViewModel.currentDegreeOrientation.value = toTalDegreeOrientation
    }
}

fun CompassFragment.onClickListener() {
    binding.layoutButtonSearch.setPreventDoubleClickScaleView {
        safeNav(
            R.id.compassFragment,
            R.id.action_compassFragment_to_searchFragment,
        )
    }
}

fun CompassFragment.showAds() {
    safePreloadAds(
        spaceNameConfig = "compass-home",
        spaceNameAds = "compass-1ID_interstitial",
    )

    showLoadedNative(
        spaceNameConfig = "compass1",
        spaceName = "compass1_native",
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {
            isNeedShowReloadAds = true
            safePreloadAds(
                spaceNameConfig = "compass2",
                spaceNameAds = "compass2_native",
            )
        },
    )
}

fun CompassFragment.showReloadAds() {
    if (isNeedShowReloadAds && !isShowedReloadAds) {
        isShowedReloadAds = true
        isNeedShowReloadAds = false
        showLoadedNative(
            spaceNameConfig = "compass2",
            spaceName = "compass2_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }
}
