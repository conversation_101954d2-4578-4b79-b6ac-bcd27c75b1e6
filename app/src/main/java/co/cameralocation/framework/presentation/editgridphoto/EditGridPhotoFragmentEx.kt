package co.cameralocation.framework.presentation.editgridphoto

import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import co.cameralocation.R
import co.cameralocation.framework.presentation.common.onSystemBackEvent
import co.cameralocation.framework.presentation.model.gridphoto.GridLayout
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.framework.presentation.model.pickphoto.DevicePhoto
import co.cameralocation.util.Constant
import co.cameralocation.util.displayToast
import co.cameralocation.util.isAllPermissionGranted
import co.cameralocation.util.parcelable
import co.cameralocation.util.parcelableArrayList
import co.cameralocation.util.requestPermission
import co.cameralocation.util.setPreventDoubleClick
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

fun EditGridPhotoFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
    onSystemBackEvent {
        backEvent()
    }
}

fun EditGridPhotoFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "gridedit-back",
        spaceName = "grid-1ID_interstitial",
        destinationToShowAds = R.id.editGridPhotoFragment,
        isShowLoadingView = true,
        timeShowLoadingView = 0,
        isScreenType = false,
        navOrBack = {
            runCatching {
                findNavController().navigateUp()
            }
        },
        onCloseAds = {},
    )
}

fun EditGridPhotoFragment.showInterEditAndDoAction(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "gridedit-edit-loco-grid",
        spaceName = "grid-1ID_interstitial",
        destinationToShowAds = R.id.editGridPhotoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action.invoke()
        },
        onCloseAds = {},
    )
}

fun EditGridPhotoFragment.setupGridData() {
    val gridLayout = arguments?.parcelable<GridLayout>(Constant.KEY_GRID_LAYOUT)
    val selectedPhotos =
        arguments?.parcelableArrayList<DevicePhoto>(Constant.KEY_SELECTED_PHOTOS) ?: emptyList()

    if (gridLayout != null) {
        binding.gridLayoutView.post {
            viewModel.setGridData(
                requireContext(),
                gridLayout,
                selectedPhotos,
                binding.gridLayoutView.width,
                binding.gridLayoutView.height,
            )
        }
    }
}

fun EditGridPhotoFragment.setupButtons() {
    binding.llEditLocation.setPreventDoubleClick {
        viewModel.navigateToEditLocation()
    }

    binding.llEditGrid.setPreventDoubleClick {
        viewModel.navigateToEditGrid()
    }

    binding.btnSaveAs.setPreventDoubleClick {
        viewModel.saveGridPhoto(
            context = requireContext(),
            bitmap = binding.gridLayoutView.createBitmapFromView(),
            locationInfo = commonViewModel.getDefaultLocationInfo(),
        )
    }
}

fun EditGridPhotoFragment.observeUiEvent() {
    viewModel.uiEvent
        .onEach { event ->
            when (event) {
                is EditGridPhotoViewModel.EditGridPhotoUiEvent.NavigateToEditLocation -> {
                    navigateToEditLocation()
                }

                is EditGridPhotoViewModel.EditGridPhotoUiEvent.NavigateToEditGrid -> {
                    backToEditGridScreen()
                }

                is EditGridPhotoViewModel.EditGridPhotoUiEvent.GridPhotoSaved -> {
                    navigateToSaveGridPhoto()
                }

                is EditGridPhotoViewModel.EditGridPhotoUiEvent.Error -> {
                    displayToast(event.message)
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditGridPhotoFragment.navigateToEditLocation() {
    // Navigate to location selection screen
    showInterEditAndDoAction {
        safeNavInter(
            R.id.editGridPhotoFragment,
            R.id.action_editGridPhotoFragment_to_editLocationNewFragment,
        )
    }
}

fun EditGridPhotoFragment.backToEditGridScreen() {
    // Navigate back to grid selection
    showInterEditAndDoAction {
        runCatching {
            findNavController().popBackStack(R.id.gridPhotoFragment, false)
        }
    }
}

fun EditGridPhotoFragment.navigateToSaveGridPhoto() {
    showLoadedInter(
        spaceNameConfig = "gridedit-save",
        spaceName = "grid-1ID_interstitial",
        destinationToShowAds = R.id.editGridPhotoFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            safeNavInter(
                R.id.editGridPhotoFragment,
                R.id.action_editGridPhotoFragment_to_savePhotoFragment,
            )
        },
        onCloseAds = {},
    )
}

fun EditGridPhotoFragment.observeGridData() {
    combine(
        commonViewModel.commonUiState.map { it.defaultLocationInfoWithLayoutItem }.filterNotNull(),
        viewModel.uiState.map { it.gridLayout }.filterNotNull(),
        viewModel.uiState.map { it.selectedPhotos }.filterNotNull(),
    ) { defaultLocationInfoWithLayoutItem, gridLayout, selectedPhotos ->
        binding.gridLayoutView.apply {
            setGridLayoutData(
                gridLayout.layoutType,
                selectedPhotos,
            )
            setViewLocationInfo(
                LocationInfoWithLayoutItem(
                    id = null,
                    locationInfo = defaultLocationInfoWithLayoutItem.locationInfo,
                    layoutItem = defaultLocationInfoWithLayoutItem.layoutItem,
                ),
            )
        }
    }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditGridPhotoFragment.setUpCurrentLocation() {
    if (!isAllPermissionGranted(permissions)) {
        Timber.d("setupMap: isAllPermissionGranted: false")
        requestPermission(permissions) {}
        return
    }

    viewModel.getCurrentLocation(
        onSuccess = { locationInfo ->
            // Use Maps KTX to get current location
            Timber.d("setupMap: setCurrentLocation: $locationInfo")

            // Set default location info if not set yet
            if (commonViewModel.getDefaultLocationInfo() == null) {
                commonViewModel.requestCreateDefaultLayoutItemDataWhenCreateNewLocation(locationInfo)
            }
            commonViewModel.requestUpdateTimestampForDefaultLocationInfoWithLayoutItem()
        },
        onError = { e ->
            displayToast(getString(R.string.error_getting_location, e?.message))
        },
    )
}

fun EditGridPhotoFragment.showAds() {
    safePreloadAds(
        listSpaceNameConfig = listOf("gridedit-back", "gridedit-edit-loco-grid", "gridedit-save"),
        spaceNameAds = "grid-1ID_interstitial",
    )

    if (AdsConstant.listConfigAds["gridedit"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
        showLoadedNative(
            spaceNameConfig = "gridedit",
            spaceName = "gridedit_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }

    if (AdsConstant.listConfigAds["gridedit"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER_ADAPTIVE) {
        showLoadedBannerAdaptive(
            spaceNameConfig = "gridedit",
            spaceName = "gridedit_adaptive",
            ratioView = "360:70",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }
}
