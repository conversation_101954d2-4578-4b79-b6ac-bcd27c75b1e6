package co.cameralocation.framework.presentation.chooselocation

import android.Manifest
import android.content.DialogInterface
import android.os.Bundle
import android.os.Parcelable
import android.util.DisplayMetrics
import android.view.KeyEvent
import androidx.fragment.app.viewModels
import co.cameralocation.R
import co.cameralocation.databinding.FragmentChooseLocationBinding
import co.cameralocation.framework.manager.LocationManager
import co.cameralocation.framework.presentation.chooselocation.adapter.PlacesAdapter
import co.cameralocation.framework.presentation.common.BaseDialogFragment
import co.cameralocation.framework.presentation.editlayout.backEvent
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.util.Constant
import co.cameralocation.util.parcelable
import com.google.android.gms.maps.GoogleMap
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.parcelize.Parcelize
import javax.inject.Inject

@AndroidEntryPoint
class ChooseLocationDialogFragment : BaseDialogFragment<FragmentChooseLocationBinding>(
    R.layout.fragment_choose_location
) {
    @Inject
    lateinit var locationManager: LocationManager

    val viewModel: ChooseLocationViewModel by viewModels()

    var onUpdateLocation: ((LocationInfoWithLayoutItem) -> Unit)? = null

    var onRequestReloadData: (() -> Unit)? = null

    var isUserClickLocationItem = false

    var googleMap: GoogleMap? = null

    val permissions = listOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
    )

    lateinit var placesAdapter: PlacesAdapter

    val changeLocationScreenType: ChooseLocationScreenType? by lazy {
        arguments?.parcelable(Constant.KEY_CHANGE_LOCATION_MODE)
    }

    val isCreateNewLocation by lazy {
        changeLocationScreenType is ChooseLocationScreenType.CreateNew
    }

    val isEditLocationGlobally by lazy {
        changeLocationScreenType is ChooseLocationScreenType.EditLocationGlobally
    }

    val createdLocationInfoWithLayoutItem: LocationInfoWithLayoutItem?
        get() = (changeLocationScreenType as? ChooseLocationScreenType.CreateNew)?.locationInfoWithLayoutItem

    val updatedLocationInfoWithLayoutItem: LocationInfoWithLayoutItem?
        get() = (changeLocationScreenType as? ChooseLocationScreenType.EditLocationGlobally)?.locationInfoWithLayoutItem

    fun setUpDialogSize() {
        val displayMetrics = DisplayMetrics()
        dialog?.window?.windowManager?.defaultDisplay?.getMetrics(displayMetrics)
        // Lấy chiều rộng và chiều cao màn hình
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        // Tính toán chiều rộng và chiều cao mới theo tỷ lệ phần trăm
        val desiredWidth = screenWidth // 100% chiều rộng
        val desiredHeight = screenHeight // 100% chiều cao
        // Đặt LayoutParams cho cửa sổ dialog
        dialog?.window?.setLayout(desiredWidth, desiredHeight)
        dialog?.setCancelable(false)
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setUpDialogSize()
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        setupBackButton()
        setUpScreenTitle()
        setupMap()
        setupDoneButton()
        setUpSearchAction()
        setUpButtonMapType()

        observeSelectedLocation()
        showAds()
    }

    override fun onResume() {
        super.onResume()
        dialog?.setOnKeyListener(
            object : DialogInterface.OnKeyListener {
                override fun onKey(
                    dialog: DialogInterface?,
                    keyCode: Int,
                    event: KeyEvent?,
                ): Boolean {
                    if (keyCode == KeyEvent.KEYCODE_BACK && event?.action == KeyEvent.ACTION_UP) {
                        backEvent(isShowAds = true)
                        return true
                    }
                    return false
                }
            },
        )
    }

    class Builder {
        private var changeLocationScreenType: ChooseLocationScreenType? = null
        private var onUpdateLocation: ((LocationInfoWithLayoutItem) -> Unit)? = null
        private var onRequestReloadData: (() -> Unit)? = null

        fun setChangeLocationScreenType(type: ChooseLocationScreenType) = apply {
            this.changeLocationScreenType = type
        }

        fun setOnUpdateLocation(listener: (LocationInfoWithLayoutItem) -> Unit) = apply {
            this.onUpdateLocation = listener
        }

        fun setOnRequestReloadData(listener: () -> Unit) = apply {
            this.onRequestReloadData = listener
        }

        fun build() = ChooseLocationDialogFragment().apply {
            arguments = Bundle().apply {
                <EMAIL>?.let { changeLocationScreenType ->
                    putParcelable(Constant.KEY_CHANGE_LOCATION_MODE, changeLocationScreenType as? Parcelable)
                }
            }

            <EMAIL>?.let { onUpdateLocation ->
                <EMAIL> = onUpdateLocation
            }

            <EMAIL>?.let { onRequestReloadData ->
                <EMAIL> = onRequestReloadData
            }
        }
    }

    companion object {
        const val TAG = "ChooseLocationDialogFragment"
    }
}

sealed class ChooseLocationScreenType {
    @Parcelize
    data class CreateNew(
        val locationInfoWithLayoutItem: LocationInfoWithLayoutItem? = null
    ) : ChooseLocationScreenType(), Parcelable

    @Parcelize
    data class EditLocationGlobally(
        val locationInfoWithLayoutItem: LocationInfoWithLayoutItem? = null
    ) : ChooseLocationScreenType(), Parcelable
}
