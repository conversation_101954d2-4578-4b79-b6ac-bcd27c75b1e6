package co.cameralocation.framework.presentation.pickphoto

import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import co.cameralocation.databinding.FragmentPickPhotoBinding
import co.cameralocation.framework.presentation.common.BaseFragment
import co.cameralocation.framework.presentation.pickphoto.adapter.DevicePhotoAdapter
import co.cameralocation.framework.presentation.pickphoto.adapter.SelectedPhotoAdapter
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class PickPhotoFragment :
    BaseFragment<FragmentPickPhotoBinding, PickPhotoViewModel>(
        FragmentPickPhotoBinding::inflate,
        PickPhotoViewModel::class.java,
    ) {
    lateinit var devicePhotoAdapter: DevicePhotoAdapter
    lateinit var selectedPhotoAdapter: SelectedPhotoAdapter

    val requestPermissionLauncher =
        registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions(),
        ) { permissions ->
            val allGranted = permissions.all { it.value }
            if (allGranted) {
                loadPhotos()
            }
        }
    var isShowingNative2 = false

    override fun init(view: View) {
        setupBackButton()
        setupGridLayout()
        setupPhotoSourceSpinner()
        setupDevicePhotoAdapter()
        setupSelectedPhotoAdapter()
        setupButtons()
        checkPermissionsAndLoadPhotos()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeDevicePhotoListChanged()
        observeSelectedPhotoListChanged()
        observeLoadingState()
        observeNextButtonState()
        observePhotoSourceMode()
        observeUiEvent()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        isShowingNative2 = false
    }

    companion object {
        const val TAG = "PickPhotoFragment"
    }
}
