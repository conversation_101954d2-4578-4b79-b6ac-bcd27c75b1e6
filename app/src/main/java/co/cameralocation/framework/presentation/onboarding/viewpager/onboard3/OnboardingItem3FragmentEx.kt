package co.cameralocation.framework.presentation.onboarding.viewpager.onboard3

import co.cameralocation.framework.presentation.common.onSystemBackEvent
import pion.datlt.libads.utils.adsuntils.showLoadedNative

fun OnboardingItem3Fragment.setupDotsIndicator() {
    binding.dotsIndicator.setSelectedPosition(2)
}

fun OnboardingItem3Fragment.setGrantPermissionEvent() {
    binding.textGrantPermission.setOnClickListener {
        if (permissionManager.isAllPermissionGranted()) {
            commonViewModel.sendGoToNextOnboardingScreenEvent()
        } else {
            permissionManager.requestPermission(
                fragment = this,
                onAllPermissionGranted = {
                    commonViewModel.sendGoToNextOnboardingScreenEvent()
                },
            )
        }
    }
}

fun OnboardingItem3Fragment.setSkipEvent() {
    binding.textSkip.setOnClickListener {
        commonViewModel.sendGoToNextOnboardingScreenEvent()
    }
}

fun OnboardingItem3Fragment.onBackEvent() {
    onSystemBackEvent {
        commonViewModel.sendGoToPreviousOnboardingScreenEvent()
    }
}

fun OnboardingItem3Fragment.showAds() {
    showLoadedNative(
        spaceNameConfig = "onboard3",
        spaceName = "onboard3_native",
        includeHasBeenOpened = true,
        layoutToAttachAds = binding.adViewGroup,
        layoutContainAds = binding.layoutAds,
        onAdsClick = {},
    )
}
