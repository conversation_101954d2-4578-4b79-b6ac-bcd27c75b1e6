package co.cameralocation.framework.presentation.detailvideolive

import android.view.View
import androidx.activity.addCallback
import androidx.navigation.fragment.findNavController
import co.cameralocation.R
import co.cameralocation.framework.presentation.model.livevideo.VideoLiveLocal
import co.cameralocation.util.displayToast
import co.cameralocation.util.extractYoutubeVideoId
import co.cameralocation.util.setPreventDoubleClick
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.YouTubePlayer
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.AbstractYouTubePlayerListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.listeners.FullscreenListener
import com.pierfrancescosoffritti.androidyoutubeplayer.core.player.options.IFramePlayerOptions
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import java.text.NumberFormat
import java.util.Locale
import kotlin.text.get

fun DetailVideoLiveFragment.onBackEvent() {
    binding.ivBack.setPreventDoubleClick {
        backEvent()
    }
    activity?.onBackPressedDispatcher?.addCallback(this, true) {
        backEvent()
    }
}

fun DetailVideoLiveFragment.backEvent() {
    showLoadedInter(
        spaceNameConfig = "watchlive-back",
        spaceName = "videolive-1ID_interstitial",
        destinationToShowAds = R.id.detailVideoLiveFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            runCatching {
                val navController = findNavController()
                if (runCatching { navController.getBackStackEntry(R.id.listVideoLiveNewFragment) }.isSuccess) {
                    runCatching {
                        navController.popBackStack(R.id.listVideoLiveNewFragment, false)
                    }
                } else {
                    runCatching {
                        navController.popBackStack()
                    }
                }
            }
        },
        onCloseAds = {},
    )
}

fun DetailVideoLiveFragment.setUpYouTubeView() {
    val iFramePlayerOptions =
        IFramePlayerOptions
            .Builder()
            .controls(1)
            .fullscreen(1)
            .build()
    binding.youtubePlayerView.enableAutomaticInitialization = false

    binding.youtubePlayerView.addFullscreenListener(
        object : FullscreenListener {
            override fun onEnterFullscreen(
                fullscreenView: View,
                exitFullscreen: () -> Unit,
            ) {
                isFullscreen = true

                // the video will continue playing in fullscreenView
                binding.youtubePlayerView.visibility = View.GONE
                binding.fullScreenViewContainer.visibility = View.VISIBLE
                binding.fullScreenViewContainer.addView(fullscreenView)

                // requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            }

            override fun onExitFullscreen() {
                isFullscreen = false

                // the video will continue playing in the player
                binding.youtubePlayerView.visibility = View.VISIBLE
                binding.fullScreenViewContainer.visibility = View.GONE
                binding.fullScreenViewContainer.removeAllViews()
            }
        },
    )

    binding.youtubePlayerView.initialize(
        object : AbstractYouTubePlayerListener() {
            override fun onReady(youTubePlayer: YouTubePlayer) {
                <EMAIL> = youTubePlayer
                youTubePlayer.loadVideo(extractYoutubeVideoId(videoLiveLocal?.linkVideo ?: ""), 0f)
            }
        },
        iFramePlayerOptions,
    )

    lifecycle.addObserver(binding.youtubePlayerView)
}

fun DetailVideoLiveFragment.initView() {
    if (videoLiveLocal == null) {
        findNavController().navigateUp()
        displayToast(R.string.something_error)
        return
    }

    binding.tvName.text =
        buildString {
            append(getString(R.string.street_camera))
            append(" ")
            append((videoLiveLocal!!.id).toString().padStart(2, '0'))
        }

    binding.tvCount.text =
        buildString {
            append(NumberFormat.getNumberInstance(Locale.US).format(videoLiveLocal!!.watchCount))
            append(" ")
            append(binding.root.context.getString(R.string.views))
        }
}

fun DetailVideoLiveFragment.setUpAdapter() {
    binding.rvMain.adapter = adapter
    adapter.setListener(this)
}

fun DetailVideoLiveFragment.addAdsNativeToListRecommend(filteredList: List<VideoLiveLocal>) {
    adapter.submitList(filteredList)
}

fun DetailVideoLiveFragment.showAds() {
    if (AdsConstant.listConfigAds["watchlive1"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
        showLoadedNative(
            spaceNameConfig = "watchlive1",
            spaceName = "watchlive1_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }

    if (AdsConstant.listConfigAds["watchlive1"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER_ADAPTIVE) {
        showLoadedBannerAdaptive(
            spaceNameConfig = "watchlive1",
            spaceName = "watchlive1_adaptive",
            ratioView = "360:70",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }

    showLoadedNative(
        spaceNameConfig = "watchlive2",
        spaceName = "watchlive2_native",
        layoutToAttachAds = binding.adViewGroupTop,
        layoutContainAds = binding.layoutAdsTop,
        onAdsClick = {},
    )

    safePreloadAds(
        listSpaceNameConfig = listOf("watchlive-back", "watchlive-choosecontent"),
        spaceNameAds = "videolive-1ID_interstitial",
    )
}
