package co.cameralocation.framework.presentation.compass.detail_compass

import android.annotation.SuppressLint
import android.view.animation.Animation
import android.view.animation.RotateAnimation

fun DetailCompassFragment.observeData() {
    commonViewModel.currentDegreeOrientation.observe(viewLifecycleOwner) {
        val ra = RotateAnimation(
            currentDegree,
            -it.toFloat(),
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
        ra.duration = 210
        ra.fillAfter = true
        binding.imgCompass.startAnimation(ra)
        currentDegree = -it.toFloat()

        var northAngle = it
        if (northAngle > 180) {
            northAngle = 360 - it
        }

        val south = 180 - northAngle

        val west = if (northAngle + 90 > 180) {
            northAngle + 90 - 180
        } else {
            northAngle + 90
        }

        val east = 180 - west

        binding.apply {
            tvDegreeNorth.text = northAngle.toString()
            tvDegreeSouth.text = south.toString()
            tvDegreeEast.text = east.toString()
            tvDegreeWest.text = west.toString()
        }
    }
}

@SuppressLint("SetTextI18n")
fun DetailCompassFragment.displayLocation() {
    binding.tvLocationValue.text = commonViewModel.addressUser
    commonViewModel.currentUserLocation.observe(viewLifecycleOwner) { location ->
        binding.apply {
            tvLongitudeValue.text = location.longitude.toString()
            tvLatitudeValue.text = location.latitude.toString()
        }
    }

    commonViewModel.currentDegreeOrientation.observe(viewLifecycleOwner) {
        binding.tvNavigateValue.text = "${it}*${findDirection(it)}"
    }
}

fun findDirection(degree: Int): String {
    if ((degree in 0..44) || degree == 360) {
        return "N"
    }
    if (degree in 45..89) {
        return "NE"
    }
    if (degree in 90..134) {
        return "E"
    }
    if (degree in 135..179) {
        return "SE"
    }
    if (degree in 180..224) {
        return "S"
    }
    if (degree in 225..269) {
        return "SW"
    }
    if (degree in 270..314) {
        return "W"
    }
    if (degree in 315..359) {
        return "NW"
    }
    return "Error Direction"
}