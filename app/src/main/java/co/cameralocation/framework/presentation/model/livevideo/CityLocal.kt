package co.cameralocation.framework.presentation.model.livevideo

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CityLocal(
    val id: Int,
    val name: String,
    val info: String?,
    val history: String?,
    val listTravel: List<TravelLocal>,
    val latitude: Double,
    val longitude: Double,
    val createdAt: String,
    val updatedAt: String
) : Parcelable