package co.cameralocation.framework.presentation.model.livevideo

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize
import co.cameralocation.BuildConfig

@Parcelize
data class Travel(
    @SerializedName("id")
    val id: Int,
    @SerializedName("cityId")
    val cityId: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("imageUrl")
    val imageUrl: String,
    @SerializedName("createdAt")
    val createdAt: String,
    @SerializedName("updatedAt")
    val updatedAt: String
) : Parcelable

fun Travel.toTravelLocal(): TravelLocal {
    return TravelLocal(
        id = this.id,
        cityId = this.id,
        imageUrl = BuildConfig.BASE_SATELLITE_URL + this.imageUrl,
        name = this.name,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt

    )
}