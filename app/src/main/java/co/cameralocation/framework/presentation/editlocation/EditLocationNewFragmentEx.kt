package co.cameralocation.framework.presentation.editlocation

import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import co.cameralocation.R
import co.cameralocation.framework.presentation.chooselocation.ChooseLocationDialogFragment
import co.cameralocation.framework.presentation.chooselocation.ChooseLocationScreenType
import co.cameralocation.framework.presentation.common.doActionWhenResume
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.common.onSystemBackEvent
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment
import co.cameralocation.framework.presentation.editlocation.EditLocationNewFragment.Companion.TAG
import co.cameralocation.framework.presentation.editlocation.adapter.LayoutAdapter
import co.cameralocation.framework.presentation.editlocation.adapter.LayoutItem
import co.cameralocation.framework.presentation.editlocation.adapter.LocationInfoLayoutItemType
import co.cameralocation.framework.presentation.editlocation.adapter.RecentLocationAdapter
import co.cameralocation.framework.presentation.model.editlocation.EditLocationNewTabType
import co.cameralocation.framework.presentation.model.editlocation.isLoadingDialogCompletelyVisible
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.framework.presentation.model.locationinfo.toLatLng
import co.cameralocation.util.TimeUtils
import co.cameralocation.util.changeTextColor
import co.cameralocation.util.displayToast
import co.cameralocation.util.isAllPermissionGranted
import co.cameralocation.util.marquee
import co.cameralocation.util.requestPermission
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.util.setPreventDoubleClickScaleView
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.SupportMapFragment
import com.google.maps.android.ktx.addMarker
import com.google.maps.android.ktx.awaitMap
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber

val listDefaultLayout =
    buildList {
        add(
            LayoutItem(
                id = 1,
                name = "Basic Layout Black",
                layoutItem = LocationInfoLayoutItemType.LAYOUT_1,
            ),
        )
        add(
            LayoutItem(
                id = 2,
                name = "Basic Layout Yellow",
                layoutItem = LocationInfoLayoutItemType.LAYOUT_2,
            ),
        )
    }

fun EditLocationNewFragment.initMap() {
    // Khởi tạo map sử dụng coroutines
    lifecycleScope.launch {
        val mapFragment =
            childFragmentManager
                .findFragmentById(R.id.map) as? SupportMapFragment

        // Sử dụng Maps KTX để đợi map sẵn sàng
        googleMap = mapFragment?.awaitMap()

        // Kiểm tra quyền vị trí
        loadMapWithGrantPermission(isFromRefresh = false)
    }
}

fun EditLocationNewFragment.refreshButtonEvent() {
    binding.btnRefresh.setPreventDoubleClick {
        loadMapWithGrantPermission(isFromRefresh = true)
    }
}

fun EditLocationNewFragment.loadMapWithGrantPermission(isFromRefresh: Boolean) {
    if (isAllPermissionGranted(permissions)) {
        setupMap(isFromRefresh)
    } else {
        requestPermission(permissions) {
            if (isAllPermissionGranted(permissions)) {
                setupMap(isFromRefresh)
            } else {
                displayToast(getString(R.string.location_permission_denied))
            }
        }
    }
}

fun EditLocationNewFragment.setupMap(isFromRefresh: Boolean) {
    googleMap?.let { map ->
        try {
            if (!isAllPermissionGranted(permissions)) {
                Timber.d("setupMap: isAllPermissionGranted: false")
                requestPermission(permissions) {}
                return
            }

            map.isMyLocationEnabled = true

            viewModel.getCurrentLocation(
                onSuccess = { locationInfo ->
                    userLocationInfo = locationInfo.copy()
                    launchMain {
                        Timber.d("setupMap: setCurrentLocation: $locationInfo")
                        if (commonViewModel.commonUiState.value.defaultLocationInfoWithLayoutItem == null) {
                            viewModel.setUpdatedLocationInfoWithLayoutItem(
                                commonViewModel.createDefaultLayoutItemDataWhenCreateNewLocation(
                                    locationInfo,
                                ),
                            )
                        } else {
                            Timber.d("setupMap: defaultLocationInfoWithLayoutItem != null")
                            viewModel.setUpdatedLocationInfoWithLayoutItem(
                                commonViewModel.commonUiState.value.defaultLocationInfoWithLayoutItem,
                            )
                        }

                        if (isFromRefresh) {
                            commonViewModel.requestCreateDefaultLayoutItemDataWhenCreateNewLocation(
                                locationInfo,
                            )
                            isUpdateAfterEditData = false
                            Timber.d("setupMap: isFromRefresh: true")
                            viewModel.setUpdatedLocationInfoWithLayoutItem(
                                commonViewModel.createDefaultLayoutItemDataWhenCreateNewLocation(
                                    locationInfo,
                                ),
                            )
                        }

                        val defaultLocationLatLng =
                            viewModel.getUpdatedLocationInfoWithLayoutItem()?.locationInfo?.toLatLng()
                        val currentLatLng =
                            if (!isFromRefresh && defaultLocationLatLng != null) {
                                defaultLocationLatLng
                            } else {
                                locationInfo.toLatLng()
                            }

                        map.clear()

                        // Use Maps KTX to add a marker
                        map.addMarker {
                            Timber.d("setupMap: addMarker: $currentLatLng")
                            position(currentLatLng)
                        }

                        map.moveCamera(CameraUpdateFactory.newLatLngZoom(currentLatLng, 15f))
                    }
                },
                onError = {
                    launchMain {
                        displayToast(getString(R.string.error_getting_location, it?.message))
                    }
                },
            )
        } catch (e: SecurityException) {
            displayToast(getString(R.string.error_location_permission, e.message))
        }
    }
}

fun EditLocationNewFragment.observerCurrentLocationChanged() {
    viewModel.uiState
        .map { it.updateLocationInfoWithLayoutItem }
        .filterNotNull()
        .distinctUntilChanged()
        .onEach { locationInfoWithLayoutItem ->
            binding.currentLocationName.text = locationInfoWithLayoutItem.locationInfo.name
            binding.currentLocationName.marquee()
            binding.currentLocationCoordinates.text =
                "${locationInfoWithLayoutItem.locationInfo.latitude}, ${locationInfoWithLayoutItem.locationInfo.longitude}"
            binding.currentLocationTimestamp.text =
                TimeUtils.formatTimestamp(locationInfoWithLayoutItem.locationInfo.timestamp)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLocationNewFragment.backEvent() {
    Timber.tag(TAG).d("backEvent: ")

    if (!isApplyDone) {
        commonViewModel.resetToUnsavedPreviousItem()
    }

    showLoadedInter(
        spaceNameConfig = "edit-back",
        spaceName = "camera-1ID_interstitial",
        destinationToShowAds = R.id.editLocationNewFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            runCatching {
                findNavController().navigateUp()
            }
        },
        onCloseAds = {},
    )
}

fun EditLocationNewFragment.showInterChooseTab(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "edit-choosetab",
        spaceName = "camera-1ID_interstitial",
        destinationToShowAds = R.id.editLocationNewFragment,
        isShowLoadingView = true,
        isScreenType = false,
        navOrBack = {
            action()
        },
        onCloseAds = {},
    )
}

fun EditLocationNewFragment.onClickBackEvent() {
    Timber.tag(TAG).d("backEvent: ")
    if (!isUpdateAfterEditData) {
        backEvent()
        return
    }
    AlertDialog
        .Builder(requireContext())
        .setTitle(getString(R.string.alert_title))
        .setMessage(getString(R.string.alert_exit_message))
        .setPositiveButton(getString(R.string.yes)) { dialog, which ->
            backEvent()
        }.setNegativeButton(getString(R.string.no)) { dialog, which ->
            dialog.dismiss()
        }.show()
}

fun EditLocationNewFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        onClickBackEvent()
    }

    onSystemBackEvent {
        onClickBackEvent()
    }
}

fun EditLocationNewFragment.setupRecyclerViewRecentLocation() {
    recentLocationAdapter =
        RecentLocationAdapter().apply {
            onItemClick = { locationInfoWithLayoutItem ->
                isUpdateAfterEditData = true
                Timber.d("onItemClick: $locationInfoWithLayoutItem")
                viewModel.setUpdatedLocationInfoWithLayoutItem(
                    locationInfoWithLayoutItem.copy(
                        locationInfo =
                            locationInfoWithLayoutItem.locationInfo.copy(
                                timestamp = System.currentTimeMillis(), // Always use realtime when use recent location
                            ),
                    ),
                )
                setUseButtonWithMarker(locationInfoWithLayoutItem)
            }
            onUseButtonClick = { locationInfoWithLayoutItem ->
                isUpdateAfterEditData = true
                Timber.d("onUseButtonClick: $locationInfoWithLayoutItem")
                viewModel.setUpdatedLocationInfoWithLayoutItem(
                    locationInfoWithLayoutItem.copy(
                        locationInfo =
                            locationInfoWithLayoutItem.locationInfo.copy(
                                timestamp = System.currentTimeMillis(), // Always use realtime when use recent location
                            ),
                    ),
                )
                setUseButtonWithMarker(locationInfoWithLayoutItem)
            }
            onEditButtonClick = { locationInfoWithLayoutItem ->
                showLoadedInter(
                    spaceNameConfig = "edit-location-edit",
                    spaceName = "camera-1ID_interstitial",
                    destinationToShowAds = R.id.editLocationNewFragment,
                    isShowLoadingView = true,
                    isScreenType = false,
                    navOrBack = {
                        isUpdateAfterEditData = true
                        Timber.d("onEditButtonClick: $locationInfoWithLayoutItem")
                        viewModel.setUpdatedLocationInfoWithLayoutItem(
                            locationInfoWithLayoutItem.copy(
                                locationInfo =
                                    locationInfoWithLayoutItem.locationInfo.copy(
                                        timestamp = System.currentTimeMillis(), // Always use realtime when use recent location
                                    ),
                            ),
                        )
                        navigateToChangeLocationLocalEditMode(locationInfoWithLayoutItem)
                    },
                    onCloseAds = {},
                )
            }
            onDeleteButtonClick = { locationInfoWithLayoutItem ->
                viewModel.deleteLocation(locationInfoWithLayoutItem) {
                    launchMain {
                        val locationName = locationInfoWithLayoutItem.locationInfo.name
                        displayToast(getString(R.string.delete_location_successfully, locationName))
                        viewModel.loadRecentLocations()
                    }
                }
            }
        }

    binding.recentLocationRecyclerView.apply {
        layoutManager = LinearLayoutManager(requireContext())
        adapter = recentLocationAdapter
        setHasFixedSize(true)
    }
}

fun EditLocationNewFragment.navigateToChangeLocationLocalEditMode(locationInfoWithLayoutItem: LocationInfoWithLayoutItem) {
    createChooseLocationDialogFragment(
        ChooseLocationScreenType.EditLocationGlobally(
            locationInfoWithLayoutItem,
        ),
    ).show(childFragmentManager, TAG)
}

fun EditLocationNewFragment.setUseButtonWithMarker(locationInfoWithLayoutItem: LocationInfoWithLayoutItem) {
    googleMap?.let { map ->
        map.clear()
        map.addMarker {
            position(locationInfoWithLayoutItem.locationInfo.toLatLng())
        }
        map.moveCamera(
            CameraUpdateFactory.newLatLngZoom(
                locationInfoWithLayoutItem.locationInfo.toLatLng(),
                15f,
            ),
        )
    }
}

fun EditLocationNewFragment.observeRecentLocationsChanged() {
    viewModel.uiState
        .map { it.recentLocations }
        .filterNotNull()
        .distinctUntilChanged()
        .onEach { locations ->
            Timber.d("observeRecentLocationsChanged: locations: $locations")
            recentLocationAdapter.submitList(locations)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLocationNewFragment.observeLoadingDialogState() {
    viewModel.uiState
        .map { it.isLoadingDialogCompletelyVisible() }
        .distinctUntilChanged()
        .onEach { isLoading ->
            if (isLoading) {
                showHideLoading(true)
            } else {
                showHideLoading(false)
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLocationNewFragment.setupRecyclerViewEditLayout() {
    editLayoutAdapter =
        LayoutAdapter().apply {
            onEditButtonClick = { locationInfoWithLayoutItem ->
                showLoadedInter(
                    spaceNameConfig = "edit-layout-edit",
                    spaceName = "camera-1ID_interstitial",
                    destinationToShowAds = R.id.editLocationNewFragment,
                    isShowLoadingView = true,
                    isScreenType = false,
                    navOrBack = {
                        isUpdateAfterEditData = true
                        onSelectLayoutType(locationInfoWithLayoutItem)
                        showEditLayout()
                    },
                    onCloseAds = {},
                )
            }

            onItemClick = { locationInfoWithLayoutItem ->
                isUpdateAfterEditData = true
                onSelectLayoutType(locationInfoWithLayoutItem)
            }
        }

    binding.editLayoutRecyclerView.apply {
        layoutManager = LinearLayoutManager(requireContext())
        adapter = editLayoutAdapter
    }
}

fun EditLocationNewFragment.onSelectLayoutType(layoutItem: LayoutItem) {
    Timber.d("onSelectLayoutType: $layoutItem")
    viewModel.setUpdatedLocationInfoWithLayoutItem(
        viewModel.getUpdatedLocationInfoWithLayoutItem()?.copy(
            layoutItem = layoutItem,
        ),
    )
}

fun EditLocationNewFragment.createSelectedLayoutList(): List<LayoutItem> =
    listDefaultLayout.map { layoutItem ->
        layoutItem.copy(
            isSelected = layoutItem.layoutItem == commonViewModel.getDefaultLayoutItem()?.layoutItem,
        )
    }

fun EditLocationNewFragment.createSelectedLayoutListWithSelectedItem(layoutItem: LayoutItem): List<LayoutItem> {
    Timber.d("createSelectedLayoutListWithSelectedItem: $layoutItem")

    val result =
        buildList {
            add(layoutItem.copy(isSelected = true))
            addAll(listDefaultLayout.filter { it.id != layoutItem.id })
        }.sortedBy { it.id }

    return result
}

fun EditLocationNewFragment.observeChangeTabEvent() {
    viewModel.uiState
        .map { it.selectedTab }
        .distinctUntilChanged()
        .onEach { tabType ->
            when (tabType) {
                EditLocationNewTabType.CHANGE_LOCATION -> {
                    binding.clChangeLocation.isVisible = true
                    binding.clEditLayout.isVisible = false

                    binding.vSelectedTabChangeLocation.setBackgroundColor(
                        ContextCompat.getColor(
                            requireContext(),
                            R.color.blue_28abf5,
                        ),
                    )
                    binding.tvButtonTabChangeLocation.changeTextColor(R.color.blue_28abf5)

                    binding.vSelectedTabEditLayout.setBackgroundColor(
                        ContextCompat.getColor(
                            requireContext(),
                            android.R.color.transparent,
                        ),
                    )
                    binding.tvButtonTabEditLayout.changeTextColor(android.R.color.white)
                }

                EditLocationNewTabType.CHANGE_LAYOUT -> {
                    binding.clChangeLocation.isVisible = false
                    binding.clEditLayout.isVisible = true

                    binding.vSelectedTabChangeLocation.setBackgroundColor(
                        ContextCompat.getColor(
                            requireContext(),
                            android.R.color.transparent,
                        ),
                    )
                    binding.tvButtonTabChangeLocation.changeTextColor(android.R.color.white)

                    binding.vSelectedTabEditLayout.setBackgroundColor(
                        ContextCompat.getColor(
                            requireContext(),
                            R.color.blue_28abf5,
                        ),
                    )
                    binding.tvButtonTabEditLayout.changeTextColor(R.color.blue_28abf5)
                }
            }
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLocationNewFragment.onClickTabChangeLayoutEvent() {
    binding.llButtonTabEditLayout.setPreventDoubleClickScaleView {
        showInterChooseTab {
            doActionWhenResume {
                viewModel.setChangeTabEvent(EditLocationNewTabType.CHANGE_LAYOUT)
            }
        }
    }
}

fun EditLocationNewFragment.onClickTabChangeLocationEvent() {
    binding.llButtonTabChangeLocation.setPreventDoubleClickScaleView {
        showInterChooseTab {
            doActionWhenResume {
                viewModel.setChangeTabEvent(EditLocationNewTabType.CHANGE_LOCATION)
            }
        }
    }
}

fun EditLocationNewFragment.showEditLayout() {
    EditLayoutDialogFragment(
        itemData = viewModel.getUpdatedLocationInfoWithLayoutItem()?.layoutItem?.itemData,
        onUpdateDefaultTitle = {
            viewModel.updateDefaultUnknownLocationTitle(it)
        },
        onUpdateShowTitle = {
            viewModel.updateShowTitleVisibilityForLayout(it)
        },
        onUpdateShowLocation = {
            viewModel.updateShowLocationVisibilityForLayout(it)
        },
        onUpdateShowCoordinates = {
            viewModel.updateShowCoordinatesVisibilityForLayout(it)
        },
        onUpdateShowDateTime = {
            viewModel.updateShowDateTimeVisibilityForLayout(it)
        },
        onUpdateShowWeather = {
            viewModel.updateShowWeatherVisibilityForLayout(it)
        },
        onCloseDialog = { hasUpdateData ->
            if (hasUpdateData) {
                // isUpdateAfterEditData = true
                // updateLocationInfoWithLayoutItem = previousLocationInfoWithLayoutItem
            }
        },
    ).show(childFragmentManager, EditLayoutDialogFragment.TAG)
}

fun EditLocationNewFragment.onClickDoneEvent() {
    binding.btnDone.setPreventDoubleClickScaleView {
        showLoadedInter(
            spaceNameConfig = "edit-done",
            spaceName = "camera-1ID_interstitial",
            destinationToShowAds = R.id.editLocationNewFragment,
            isShowLoadingView = true,
            isScreenType = false,
            navOrBack = {
                onEditLayoutLocationGlobally()
            },
            onCloseAds = {},
        )
    }
}

fun EditLocationNewFragment.onEditLayoutLocationGlobally() {
    isApplyDone = true

    if (viewModel.getUpdatedLocationInfoWithLayoutItem()?.id == null) {
        commonViewModel.setDefaultLocationInfoWithLayoutItem(
            commonViewModel.getDefaultLocationInfoWithLayoutItem()?.copy(
                id = null,
                layoutItem = viewModel.getUpdatedLocationInfoWithLayoutItem()?.layoutItem!!,
                locationInfo = viewModel.getUpdatedLocationInfoWithLayoutItem()?.locationInfo!!,
            ),
        )
        backEvent()
        return
    }

    val updatedLocationInfoWithLayoutItem = viewModel.getUpdatedLocationInfoWithLayoutItem()
    viewModel.updateLocationToDatabase(
        locationInfoWithLayoutItem = updatedLocationInfoWithLayoutItem,
        onSuccess = {
            launchMain {
                displayToast(getString(R.string.update_location_success))
                commonViewModel.setDefaultLocationInfoWithLayoutItem(
                    updatedLocationInfoWithLayoutItem,
                )
                backEvent()
            }
        },
        onError = {
            launchMain {
                displayToast(getString(R.string.update_location_failed))
            }
        },
    )
}

fun EditLocationNewFragment.onClickAddNewLocationEvent() {
    binding.btnAddLocation.setPreventDoubleClickScaleView {
        if (userLocationInfo == null) {
            displayToast(getString(R.string.error_getting_location))
            return@setPreventDoubleClickScaleView
        }

        showLoadedInter(
            spaceNameConfig = "edit-add",
            spaceName = "camera-1ID_interstitial",
            destinationToShowAds = R.id.editLocationNewFragment,
            isShowLoadingView = true,
            timeShowLoadingView = 0,
            isScreenType = false,
            navOrBack = {
                createChooseLocationDialogFragment(
                    ChooseLocationScreenType.CreateNew(
                        commonViewModel.createDefaultLayoutItemDataWhenCreateNewLocation(
                            userLocationInfo!!,
                        ),
                    ),
                ).show(childFragmentManager, TAG)
            },
            onCloseAds = {},
        )
    }
}

fun EditLocationNewFragment.createChooseLocationDialogFragment(type: ChooseLocationScreenType): ChooseLocationDialogFragment =
    ChooseLocationDialogFragment
        .Builder()
        .setChangeLocationScreenType(type)
        .setOnUpdateLocation { locationInfoWithLayoutItem ->
            commonViewModel.setDefaultLocationInfoWithLayoutItem(locationInfoWithLayoutItem)
            viewModel.setUpdatedLocationInfoWithLayoutItem(commonViewModel.commonUiState.value.defaultLocationInfoWithLayoutItem)
        }.setOnRequestReloadData {
            viewModel.loadRecentLocations()
        }.build()

fun EditLocationNewFragment.observeLayoutItemSelectedChanged() {
    viewModel.uiState
        .map { it.updateLocationInfoWithLayoutItem }
        .distinctUntilChanged()
        .onEach { updatedLocationInfoWithLayoutItem ->
            val listLayoutAdapter =
                if (updatedLocationInfoWithLayoutItem != null) {
                    createSelectedLayoutListWithSelectedItem(updatedLocationInfoWithLayoutItem.layoutItem)
                } else {
                    createSelectedLayoutList()
                }
            editLayoutAdapter.submitList(listLayoutAdapter)
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun EditLocationNewFragment.showAds() {
    if (AdsConstant.listConfigAds["edit"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
        showLoadedNative(
            spaceNameConfig = "edit",
            spaceName = "edit_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
    if (AdsConstant.listConfigAds["edit"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER) {
        showLoadedBannerAdaptive(
            spaceNameConfig = "edit",
            spaceName = "edit_adaptive",
            ratioView = "360:70",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }

    showLoadedNative(
        spaceNameConfig = "edit-tab-location",
        spaceName = "edit-tab-location_native",
        layoutToAttachAds = binding.adViewGroupChangeLocationTop,
        layoutContainAds = binding.layoutAdsChangeLocationTop,
        onAdsClick = {},
    )

    safePreloadAds(
        listSpaceNameConfig =
            listOf(
                "edit-back",
                "edit-location-edit",
                "edit-layout-edit",
                "edit-done",
                "edit-add",
                "edit-choosetab",
            ),
        spaceNameAds = "camera-1ID_interstitial",
    )
}
