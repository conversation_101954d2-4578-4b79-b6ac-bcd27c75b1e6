package co.cameralocation.framework.presentation.editlocation.adapter

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import co.cameralocation.R
import co.cameralocation.customview.locationinfo.LocationInfoLayoutFactory
import co.cameralocation.databinding.ItemLayoutTypeChangeLayoutScreenBinding
import co.cameralocation.framework.presentation.common.BaseListAdapter
import co.cameralocation.framework.presentation.common.createDiffCallback
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.util.changeTextColor
import co.cameralocation.util.removeSelf
import co.cameralocation.util.setPreventDoubleClick

enum class LocationInfoLayoutItemType {
    LAYOUT_1,
    LAYOUT_2
}

@Parcelize
data class LayoutItem(
    val id: Int,
    val name: String,
    val layoutItem: LocationInfoLayoutItemType,
    val itemData: LayoutItemData = LayoutItemData(),
    val isSelected: Boolean = false
) : Parcelable

@Parcelize
data class LayoutItemData(
    val defaultTitle: String? = null,
    val isRequestedTitleVisible: Boolean = false,
    val isLocationVisible: Boolean = true,
    val isCoordinatesVisible: Boolean = true,
    val isDateTimeVisible: Boolean = true,
    val isWeatherVisible: Boolean = true
) : Parcelable

fun LayoutItemData.isTitleCompletelyVisible(): Boolean {
    return isRequestedTitleVisible && defaultTitle != null
}

class LayoutAdapter : BaseListAdapter<LayoutItem, ItemLayoutTypeChangeLayoutScreenBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {
    var onEditButtonClick: ((LayoutItem) -> Unit)? = null
    var onItemClick: ((LayoutItem) -> Unit)? = null

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_layout_type_change_layout_screen

    override fun bindView(
        binding: ItemLayoutTypeChangeLayoutScreenBinding,
        item: LayoutItem,
        position: Int
    ) {
        val item = getItem(position)

        val layoutView = LocationInfoLayoutFactory.createLocationInfoLayoutView(
            item.layoutItem,
            binding.root.context
        )
        binding.locationOverlay.removeAllViews()
        layoutView.removeSelf()
        binding.locationOverlay.addView(layoutView)
        layoutView.post {
            layoutView.setVisibilityEditButton(false)
        }

        binding.tvLayoutName.text = item.layoutItem.name

        binding.btnEdit.setPreventDoubleClick {
            onEditButtonClick?.invoke(item)
        }

        binding.root.setPreventDoubleClick {
            onItemClick?.invoke(item)
        }

        if (item.isSelected) {
            binding.basicLayoutButton.isSelected = true
            binding.checkIcon.isSelected = true
            binding.root.isSelected = true
            binding.tvUseThisLayout.changeTextColor(R.color.blue_28abf5)
            binding.tvLayoutName.changeTextColor(R.color.blue_28abf5)
        } else {
            binding.basicLayoutButton.isSelected = false
            binding.checkIcon.isSelected = false
            binding.root.isSelected = false
            binding.tvUseThisLayout.changeTextColor(android.R.color.white)
            binding.tvLayoutName.changeTextColor(android.R.color.white)
        }
    }

    override fun bindView(
        binding: ItemLayoutTypeChangeLayoutScreenBinding,
        item: LayoutItem,
        position: Int,
        payloads: MutableList<Any>
    ) {
        bindView(binding, item, position)
    }
}