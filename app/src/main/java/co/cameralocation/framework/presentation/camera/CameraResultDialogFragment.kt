package co.cameralocation.framework.presentation.camera

import android.content.DialogInterface
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.KeyEvent
import android.view.View
import android.widget.SeekBar
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import co.cameralocation.R
import co.cameralocation.databinding.LayoutCameraResultOverlayBinding
import co.cameralocation.framework.presentation.chooselocation.backEvent
import co.cameralocation.framework.presentation.common.BaseDialogFragment
import co.cameralocation.util.BitmapUtils
import co.cameralocation.util.setPreventDoubleClick
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import java.util.Locale

class CameraResultDialogFragment private constructor(
    private val type: CameraResultDialogFragmentType? = null,
    private val onAcceptListener: ((CameraResultDialogFragmentType) -> Unit)? = null,
    private val onCancelListener: (() -> Unit)? = null,
) : BaseDialogFragment<LayoutCameraResultOverlayBinding>(R.layout.layout_camera_result_overlay) {
    private var player: ExoPlayer? = null
    private var isPlaying = true
    private var handler: Handler? = null
    private var updateSeekBarRunnable: Runnable? = null

    val playerListener =
        object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                if (playbackState == Player.STATE_READY) {
                    // Khi player sẵn sàng, cập nhật thời lượng cho SeekBar max
                    val duration = player!!.duration
                    if (duration > 0) {
                        binding.seekBar.setMax(duration.toInt())
                    }
                    // Bắt đầu cập nhật SeekBar nếu player đang phát
                    startGetVideoDurationRunnable()
                } else if (playbackState == Player.STATE_ENDED) {
                    // Khi kết thúc, dừng cập nhật và đặt SeekBar về 0
                    stopGetVideoDurationRunnable()
                    binding.seekBar.progress = 0
                    isPlaying = false
                    binding.buttonPlay.setImageResource(R.drawable.ic_resume_video_recording_camera_screen)
                }
            }

            override fun onIsPlayingChanged(playing: Boolean) {
                isPlaying = playing
                binding.buttonPlay.setImageResource(
                    if (playing) {
                        R.drawable.ic_pause_recording_video_camera_screen
                    } else {
                        R.drawable.ic_resume_video_recording_camera_screen
                    },
                )
            }

            override fun onPlayWhenReadyChanged(
                playWhenReady: Boolean,
                reason: Int,
            ) {
                if (playWhenReady) {
                    // Bắt đầu cập nhật SeekBar khi bắt đầu hoặc tiếp tục phát
                    startGetVideoDurationRunnable()
                } else {
                    // Dừng cập nhật SeekBar khi tạm dừng
                    stopGetVideoDurationRunnable()
                }
            }
        }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        displayImage()
        displayVideo()
    }

    override fun initView(savedInstanceState: Bundle?) {
        super.initView(savedInstanceState)
        setUpDialogSize()
        showAds()
    }

    override fun addEvent(savedInstanceState: Bundle?) {
        super.addEvent(savedInstanceState)
        setupButtons()

        // Xử lý sự kiện nút play/pause tùy chỉnh (tùy chọn)
        binding.buttonPlay.setPreventDoubleClick {
            if (isPlaying) {
                player?.pause()
                binding.buttonPlay.setImageResource(R.drawable.ic_resume_video_recording_camera_screen)
            } else {
                player?.play()
                binding.buttonPlay.setImageResource(R.drawable.ic_pause_recording_video_camera_screen)
            }
            isPlaying = !isPlaying
        }

        binding.seekBar.setOnSeekBarChangeListener(
            object : SeekBar.OnSeekBarChangeListener {
                private var isTracking = false

                override fun onProgressChanged(
                    seekBar: SeekBar?,
                    progress: Int,
                    fromUser: Boolean,
                ) {
                    if (fromUser && player != null) {
                        player?.seekTo(progress.toLong())
                        binding.textCurrentTime.text = formatTime(progress.toLong())
                    }
                }

                override fun onStartTrackingTouch(seekBar: SeekBar?) {
                    // Khi người dùng bắt đầu kéo, tạm dừng cập nhật SeekBar tự động
                    isTracking = true
                    stopGetVideoDurationRunnable()
                }

                override fun onStopTrackingTouch(seekBar: SeekBar?) {
                    // Khi người dùng dừng kéo, seek player đến vị trí mới và tiếp tục cập nhật
                    isTracking = false
                    if (player != null) {
                        seekBar?.progress?.toLong()?.let { player?.seekTo(it) }
                    }
                    // Tiếp tục cập nhật SeekBar
                    startGetVideoDurationRunnable()
                }
            },
        )
    }

    private fun displayVideo() {
        if (type is CameraResultDialogFragmentType.Video) {
            binding.ivCapturedImage.visibility = View.GONE
            binding.clVideoContainer.visibility = View.VISIBLE
            initializeExoPlayer()
            binding.tvTitle.text = getString(R.string.preview_video)
        }
    }

    private fun showAds() {
        showLoadedNative(
            spaceNameConfig = "camera-preview",
            spaceName = "camera-preview_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
        safePreloadAds(
            listSpaceNameConfig = listOf("camera-preview-back", "camera-preview-save"),
            spaceNameAds = "camera-1ID_interstitial",
        )
    }

    private fun startGetVideoDurationRunnable() {
        if (binding.playerView.player != null && binding.playerView.player!!.playWhenReady) {
            updateSeekBarRunnable?.let { handler?.post(it) }
        }
    }

    private fun stopGetVideoDurationRunnable() {
        updateSeekBarRunnable?.let { handler?.removeCallbacks(it) }
    }

    @OptIn(UnstableApi::class)
    private fun initializeExoPlayer() {
        if (type !is CameraResultDialogFragmentType.Video) return

        if (player != null) {
            releasePlayer()
        }

        if (updateSeekBarRunnable != null) {
            handler?.removeCallbacks(updateSeekBarRunnable!!)
            updateSeekBarRunnable = null
        }

        // Cấu hình ExoPlayer
        binding.playerView.useController = false // Hide controller
        binding.textCurrentTime.text = formatTime(0) // Init state
        val trackSelector = DefaultTrackSelector(requireContext())
        trackSelector.setParameters(
            trackSelector
                .buildUponParameters()
                .setMaxVideoSize(854, 480) // Giới hạn HD thành 480p
                .setMaxVideoBitrate(1000000), // Giới hạn bitrate 1Mbps
        )
        val loadControl =
            DefaultLoadControl
                .Builder()
                .setBufferDurationsMs(
                    5000, // minBufferMs
                    10000, // maxBufferMs
                    1000, // bufferForPlaybackMs
                    2000, // bufferForPlaybackAfterRebufferMs
                ).build()

        player =
            ExoPlayer
                .Builder(requireContext())
                .setTrackSelector(trackSelector)
                .setLoadControl(loadControl)
                .build()
        // Để video hiện tại lặp lại sau khi kết thúc
        player?.repeatMode = Player.REPEAT_MODE_ONE
        binding.playerView.player = player
        handler = Handler(Looper.getMainLooper())
        updateSeekBarRunnable =
            object : Runnable {
                override fun run() {
                    if (player != null && player!!.isPlaying) {
                        // Cập nhật tiến trình của SeekBar
                        val currentPosition = player!!.currentPosition
                        binding.seekBar.progress = currentPosition.toInt()
                        binding.textCurrentTime.text = formatTime(currentPosition)
                    }
                    // Lập lịch chạy lại sau mỗi 200ms
                    handler?.postDelayed(this, 200)
                }
            }

        // Tạo MediaItem từ URI
        val mediaItem = MediaItem.fromUri(type.videoUri)
        player?.setMediaItem(mediaItem)
        player?.prepare()

        if (isPlaying) {
            // Bắt đầu phát ngay khi sẵn sàng
            player?.playWhenReady = true
        }
    }

    private fun setUpDialogSize() {
        val displayMetrics = DisplayMetrics()
        dialog
            ?.window
            ?.windowManager
            ?.defaultDisplay
            ?.getMetrics(displayMetrics)
        // Lấy chiều rộng và chiều cao màn hình
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        // Tính toán chiều rộng và chiều cao mới theo tỷ lệ phần trăm
        val desiredWidth = screenWidth // 100% chiều rộng
        val desiredHeight = screenHeight // 100% chiều cao
        // Đặt LayoutParams cho cửa sổ dialog
        dialog?.window?.setLayout(desiredWidth, desiredHeight)
        dialog?.setCancelable(false)
    }

    private fun setupButtons() {
        binding.btnAccept.setPreventDoubleClick {
            showLoadedInter(
                spaceNameConfig = "camera-preview-save",
                spaceName = "camera-1ID_interstitial",
                destinationToShowAds = R.id.cameraFragment,
                isShowLoadingView = true,
                isScreenType = false,
                navOrBack = {
                    onAcceptListener?.invoke(type ?: return@showLoadedInter)
                    dismiss()
                },
                onCloseAds = {},
            )
        }

        binding.btnCancel.setPreventDoubleClick {
            onCancelListener?.invoke()
            dismiss()
        }
    }

    private fun displayImage() {
        if (type is CameraResultDialogFragmentType.Picture) {
            binding.ivCapturedImage.visibility = View.VISIBLE
            binding.clVideoContainer.visibility = View.GONE
            binding.ivCapturedImage.post {
                binding.ivCapturedImage.setImageBitmap(
                    BitmapUtils.resizeBitmapToFitView(
                        type.bitmap,
                        binding.ivCapturedImage.width,
                        binding.ivCapturedImage.height,
                    ),
                )
            }
            binding.tvTitle.text = getString(R.string.preview_photo)
        }
    }

    private fun formatTime(milliseconds: Long): String {
        val seconds = (milliseconds / 1000) % 60
        val minutes = (milliseconds / (1000 * 60)) % 60
        return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        releasePlayer()
        stopGetVideoDurationRunnable()
    }

    fun releasePlayer() {
        player?.removeListener(playerListener)
        player?.release()
        player = null
    }

    override fun onStop() {
        super.onStop()
        player?.removeListener(playerListener)
        // Tạm dừng cập nhật SeekBar khi Activity bị tạm dừng
        stopGetVideoDurationRunnable()
    }

    override fun onResume() {
        super.onResume()
        startGetVideoDurationRunnable()
        dialog?.setOnKeyListener(
            object : DialogInterface.OnKeyListener {
                override fun onKey(
                    dialog: DialogInterface?,
                    keyCode: Int,
                    event: KeyEvent?,
                ): Boolean {
                    if (keyCode == KeyEvent.KEYCODE_BACK && event?.action == KeyEvent.ACTION_UP) {
                        backEvent()
                        return true
                    }
                    return false
                }
            },
        )
    }

    private fun backEvent() {
        showLoadedInter(
            spaceNameConfig = "camera-preview-back",
            spaceName = "camera-1ID_interstitial",
            destinationToShowAds = R.id.cameraFragment,
            isShowLoadingView = true,
            timeShowLoadingView = 0,
            isScreenType = false,
            navOrBack = {
                dismiss()
            },
            onCloseAds = {},
        )
    }

    override fun onPause() {
        super.onPause()
        // Tạm dừng cập nhật SeekBar khi Activity bị tạm dừng
        stopGetVideoDurationRunnable()
    }

    override fun onStart() {
        super.onStart()
        // Nếu player chưa được tạo hoặc đã giải phóng (do onStop), tạo lại
        if (player == null) {
            initializeExoPlayer()
        }
        player?.addListener(playerListener)
        // Tiếp tục phát nếu trước đó đang phát (quản lý trạng thái onStop)
        startGetVideoDurationRunnable()
    }

    class Builder {
        private var type: CameraResultDialogFragmentType? = null
        private var onAcceptListener: ((CameraResultDialogFragmentType) -> Unit)? = null
        private var onCancelListener: (() -> Unit)? = null

        fun setType(type: CameraResultDialogFragmentType) =
            apply {
                this.type = type
            }

        fun setOnAcceptListener(listener: (CameraResultDialogFragmentType) -> Unit) =
            apply {
                this.onAcceptListener = listener
            }

        fun setOnCancelListener(listener: () -> Unit) =
            apply {
                this.onCancelListener = listener
            }

        fun build() =
            CameraResultDialogFragment(
                type,
                onAcceptListener,
                onCancelListener,
            )
    }
}

sealed class CameraResultDialogFragmentType {
    data class Picture(
        val bitmap: Bitmap,
    ) : CameraResultDialogFragmentType()

    data class Video(
        val videoUri: Uri,
    ) : CameraResultDialogFragmentType()
}
