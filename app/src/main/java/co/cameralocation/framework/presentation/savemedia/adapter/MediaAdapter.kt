package co.cameralocation.framework.presentation.savemedia.adapter

import androidx.databinding.ViewDataBinding
import com.bumptech.glide.Glide
import co.cameralocation.R
import co.cameralocation.databinding.ItemMediaDateHeaderBinding
import co.cameralocation.databinding.ItemMediaPhotoBinding
import co.cameralocation.databinding.ItemMediaVideoBinding
import co.cameralocation.framework.presentation.common.BaseListAdapter
import co.cameralocation.framework.presentation.common.createDiffCallback
import co.cameralocation.framework.presentation.model.media.CameraGPSSavedMediaItem
import co.cameralocation.framework.presentation.model.media.MediaItemWithHeader
import co.cameralocation.framework.presentation.model.media.CameraGPSSavedMediaType
import co.cameralocation.util.setPreventDoubleClick
import java.util.concurrent.TimeUnit

class MediaAdapter : BaseListAdapter<MediaItemWithHeader, ViewDataBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem ->
            when {
                oldItem is MediaItemWithHeader.Header && newItem is MediaItemWithHeader.Header ->
                    oldItem.date == newItem.date
                oldItem is MediaItemWithHeader.Item && newItem is MediaItemWithHeader.Item ->
                    oldItem.cameraGPSSavedMediaItem.id == newItem.cameraGPSSavedMediaItem.id
                else -> false
            }
        },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {
    var onPhotoClick: ((CameraGPSSavedMediaItem) -> Unit)? = null
    var onVideoClick: ((CameraGPSSavedMediaItem) -> Unit)? = null

    companion object {
        private const val VIEW_TYPE_HEADER = 0
        private const val VIEW_TYPE_PHOTO = 1
        private const val VIEW_TYPE_VIDEO = 2
    }

    override fun getItemViewType(position: Int): Int {
        return when (val item = getItem(position)) {
            is MediaItemWithHeader.Header -> VIEW_TYPE_HEADER
            is MediaItemWithHeader.Item -> {
                when (item.cameraGPSSavedMediaItem.type) {
                    CameraGPSSavedMediaType.PHOTO -> VIEW_TYPE_PHOTO
                    CameraGPSSavedMediaType.VIDEO -> VIEW_TYPE_VIDEO
                }
            }
        }
    }

    override fun getLayoutRes(viewType: Int): Int {
        return when (viewType) {
            VIEW_TYPE_HEADER -> R.layout.item_media_date_header
            VIEW_TYPE_PHOTO -> R.layout.item_media_photo
            VIEW_TYPE_VIDEO -> R.layout.item_media_video
            else -> throw IllegalArgumentException("Invalid view type: $viewType")
        }
    }

    override fun bindView(binding: ViewDataBinding, item: MediaItemWithHeader, position: Int) {
        when (binding) {
            is ItemMediaDateHeaderBinding -> bindHeader(binding, item as MediaItemWithHeader.Header)
            is ItemMediaPhotoBinding -> bindPhoto(binding, (item as MediaItemWithHeader.Item).cameraGPSSavedMediaItem)
            is ItemMediaVideoBinding -> bindVideo(binding, (item as MediaItemWithHeader.Item).cameraGPSSavedMediaItem)
        }
    }

    override fun bindView(
        binding: ViewDataBinding,
        item: MediaItemWithHeader,
        position: Int,
        payloads: MutableList<Any>
    ) {
        bindView(binding, item, position)
    }

    private fun bindHeader(binding: ItemMediaDateHeaderBinding, header: MediaItemWithHeader.Header) {
        binding.tvDate.text = header.date
    }

    private fun bindPhoto(binding: ItemMediaPhotoBinding, cameraGPSSavedMediaItem: CameraGPSSavedMediaItem) {
        // Load image with Glide
        Glide.with(binding.root.context)
            .load(cameraGPSSavedMediaItem.uri)
            .centerCrop()
            .into(binding.ivPhoto)

        // Set location name
        binding.tvLocation.text = cameraGPSSavedMediaItem.locationName

        // Set click listener
        binding.root.setPreventDoubleClick {
            onPhotoClick?.invoke(cameraGPSSavedMediaItem)
        }
    }

    private fun bindVideo(binding: ItemMediaVideoBinding, cameraGPSSavedMediaItem: CameraGPSSavedMediaItem) {
        // Load video thumbnail with Glide
        Glide.with(binding.root.context)
            .load(cameraGPSSavedMediaItem.uri)
            .centerCrop()
            .into(binding.ivPhoto)

        // Set location name
        binding.tvLocation.text = cameraGPSSavedMediaItem.locationName

        // Format and set duration
        cameraGPSSavedMediaItem.duration?.let { durationMs ->
            val minutes = TimeUnit.MILLISECONDS.toMinutes(durationMs)
            val seconds = TimeUnit.MILLISECONDS.toSeconds(durationMs) -
                    TimeUnit.MINUTES.toSeconds(minutes)
            binding.tvDuration.text = String.format("%02d:%02d", minutes, seconds)
        }

        // Set click listener
        binding.root.setPreventDoubleClick {
            onVideoClick?.invoke(cameraGPSSavedMediaItem)
        }
    }
}
