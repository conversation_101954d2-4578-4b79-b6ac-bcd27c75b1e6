package co.cameralocation.framework.presentation.chooselocation

import android.location.Address
import android.location.Geocoder
import android.os.Build
import android.util.Log
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import co.cameralocation.R
import co.cameralocation.framework.network.weather.WeatherResponse
import co.cameralocation.framework.presentation.chooselocation.adapter.PlacesAdapter
import co.cameralocation.framework.presentation.chooselocation.adapter.PlacesItem
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.dialog.MapStyleDialog
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo
import co.cameralocation.framework.presentation.model.locationinfo.toLatLng
import co.cameralocation.framework.repository.Result
import co.cameralocation.util.collectFlowOnView
import co.cameralocation.util.displayToast
import co.cameralocation.util.hideKeyboard
import co.cameralocation.util.isAllPermissionGranted
import co.cameralocation.util.marquee
import co.cameralocation.util.requestPermission
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.util.textChangesFlow
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.ktx.addMarker
import com.google.maps.android.ktx.awaitMap
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import pion.datlt.libads.utils.AdDef
import pion.datlt.libads.utils.AdsConstant
import pion.datlt.libads.utils.adsuntils.safePreloadAds
import pion.datlt.libads.utils.adsuntils.showLoadedBannerAdaptive
import pion.datlt.libads.utils.adsuntils.showLoadedInter
import pion.datlt.libads.utils.adsuntils.showLoadedNative
import timber.log.Timber
import java.util.Locale

fun ChooseLocationDialogFragment.setUpScreenTitle() {
    binding.titleText.text =
        when {
            isCreateNewLocation -> getString(R.string.add_location)
            else -> getString(R.string.edit_location)
        }
}

fun ChooseLocationDialogFragment.setUpButtonMapType() {
    binding.toggleMapTypeButton.setPreventDoubleClick {
        val mapStyleDialog =
            MapStyleDialog.newInstance(googleMap?.mapType ?: GoogleMap.MAP_TYPE_NORMAL)

        mapStyleDialog.setListener(
            object : MapStyleDialog.Listener {
                override fun setMapStyleDefault() {
                    googleMap?.mapType = GoogleMap.MAP_TYPE_NORMAL
                }

                override fun setMapStyleTerrain() {
                    googleMap?.mapType = GoogleMap.MAP_TYPE_TERRAIN
                }

                override fun setMapStyleSatellite() {
                    googleMap?.mapType = GoogleMap.MAP_TYPE_HYBRID
                }
            },
        )

        mapStyleDialog.show(
            childFragmentManager,
            ChooseLocationDialogFragment.TAG,
        )
    }
}

fun ChooseLocationDialogFragment.setupMap() {
    launchMain {
        val mapFragment =
            childFragmentManager.findFragmentById(R.id.mapChooseLocation) as? SupportMapFragment

        googleMap = mapFragment?.awaitMap()

        googleMap?.let { map ->
            try {
                if (!isAllPermissionGranted(permissions)) {
                    requestPermission(permissions) {}
                    return@launchMain
                }

                map.isMyLocationEnabled = true

                map.setOnMapClickListener { latLng ->
                    updateSelectedLocation(latLng)
                }

                viewModel.getCurrentLocation(
                    onSuccess = { locationInfo ->
                        Timber.d("setupMap: setCurrentLocation: $locationInfo")

                        if (isCreateNewLocation) {
                            viewModel.setLocationInfoWithLayoutItem(
                                createdLocationInfoWithLayoutItem,
                            )
                        }

                        if (isEditLocationGlobally) {
                            viewModel.setLocationInfoWithLayoutItem(
                                updatedLocationInfoWithLayoutItem,
                            )
                        }

                        updateSelectedLocation(
                            viewModel.getLocationInfoWithLayoutItem()?.locationInfo?.toLatLng()
                                ?: locationInfo.toLatLng(),
                        )
                    },
                    onError = { e ->
                        displayToast(getString(R.string.error_getting_location, e?.message))
                    },
                )
            } catch (e: SecurityException) {
                displayToast(getString(R.string.error_location_permission, e.message))
            }
        }
    }
}

fun ChooseLocationDialogFragment.updateSelectedLocation(
    latLng: LatLng,
    updateId: Long? = null,
) {
    Timber.d("updateSelectedLocation: latLng: $latLng, updateId: $updateId")
    viewLifecycleOwner.lifecycleScope.launch {
        val locationName =
            locationManager.getAddressFromLocation(
                requireContext(),
                latLng.latitude,
                latLng.longitude,
            )

        val result = CompletableDeferred<Result<WeatherResponse>>()
        viewModel.getCurrentWeatherByCoordinates(
            latitude = latLng.latitude,
            longitude = latLng.longitude,
            language = Locale.getDefault().language,
            onSuccess = { weatherResponse ->
                Timber.d("getCurrentWeatherByCoordinates: weatherResponse: $weatherResponse")
                result.complete(Result.Success(weatherResponse))
            },
            onError = {
                Timber.d("getCurrentWeatherByCoordinates: error")
                result.complete(Result.Error(Exception("Error getting weather")))
            },
        )

        val weatherResponse = result.await()
        val weatherData =
            when (weatherResponse) {
                is Result.Success -> weatherResponse.data
                is Result.Error -> null
            }

        val locationInfo =
            LocationInfo(
                id = updateId,
                name = locationName ?: requireContext().getString(R.string.unknown),
                latitude = latLng.latitude,
                longitude = latLng.longitude,
                temperature = weatherData?.main?.temp,
                weatherCondition = weatherData?.weather?.firstOrNull()?.main,
            )

        viewModel.setLocationInfoWithLayoutItem(
            viewModel.getLocationInfoWithLayoutItem()?.copy(
                locationInfo = locationInfo,
            ),
        )

        // Update map marker
        googleMap?.let { map ->
            map.clear()
            map.addMarker {
                position(latLng)
            }
            map.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 15f))
        }
    }
}

fun ChooseLocationDialogFragment.observeSelectedLocation() {
    viewModel.uiState
        .map { it.locationInfoWithLayoutItem?.locationInfo }
        .filterNotNull()
        .collectFlowOnView(viewLifecycleOwner, Lifecycle.State.RESUMED) { locationInfo ->
            binding.tvCurrentLocationName.text = locationInfo.name
            binding.tvCurrentLocationName.marquee()
            binding.tvCurrentLocationCoordinates.text =
                "${locationInfo.latitude}, ${locationInfo.longitude}"
        }
}

fun ChooseLocationDialogFragment.setupDoneButton() {
    binding.btnDone.setPreventDoubleClick {
        val locationInfoWithLayoutItem = viewModel.getLocationInfoWithLayoutItem()

        if (locationInfoWithLayoutItem == null) {
            displayToast(getString(R.string.please_select_location))
            return@setPreventDoubleClick
        }

        if (isCreateNewLocation) {
            viewModel.saveNewLocationToDatabase(viewModel.getLocationInfoWithLayoutItem()!!) {
                launchMain {
                    displayToast(getString(R.string.save_new_location))
                    showInterDoneEvent {
                        backEvent()
                    }
                }
            }
        } else {
            val locationInfoWithLayoutItem =
                updatedLocationInfoWithLayoutItem?.copy(
                    locationInfo = viewModel.getLocationInfoWithLayoutItem()!!.locationInfo,
                )

            viewModel.updateLocationToDatabase(
                locationInfoWithLayoutItem,
                onSuccess = {
                    launchMain {
                        displayToast(getString(R.string.update_location_success))
                        // Set default location info to global layout
                        // commonViewModel.setDefaultLocationInfoWithLayoutItem(locationInfoWithLayoutItem)
                        locationInfoWithLayoutItem?.let { onUpdateLocation?.invoke(it) }
                        showInterDoneEvent {
                            backEvent()
                        }
                    }
                },
                onError = {
                    launchMain {
                        displayToast(getString(R.string.update_location_failed))
                    }
                },
            )
        }
    }
}

fun ChooseLocationDialogFragment.showInterDoneEvent(action: () -> Unit) {
    showLoadedInter(
        spaceNameConfig = "change-location-done",
        spaceName = "camera-1ID_interstitial",
        isShowLoadingView = true,
        timeShowLoadingView = 0,
        isScreenType = false,
        navOrBack = { action.invoke() },
        onCloseAds = {},
    )
}

fun ChooseLocationDialogFragment.backEvent(isShowAds: Boolean = false) {
    val backFunction = {
        onRequestReloadData?.invoke()
        dismiss()
    }

    if (isShowAds) {
        showLoadedInter(
            spaceNameConfig = "change-location-back",
            spaceName = "camera-1ID_interstitial",
            destinationToShowAds = R.id.editLocationNewFragment,
            isShowLoadingView = true,
            timeShowLoadingView = 0,
            isScreenType = false,
            navOrBack = {
                backFunction.invoke()
            },
            onCloseAds = {},
        )
    } else {
        backFunction.invoke()
    }
}

fun ChooseLocationDialogFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent(isShowAds = true)
    }
}

fun ChooseLocationDialogFragment.createPlacesList(addresses: List<Address>): List<PlacesItem> =
    addresses.mapIndexed { index, address ->
        val placeName =
            listOfNotNull(
                address.featureName?.plus(address.thoroughfare?.let { ", $it" } ?: ""),
                address.thoroughfare,
                address.subLocality,
                address.locality,
                address.adminArea,
                address.countryName,
            ).firstOrNull() ?: "Unknown place"

        val placeAddress =
            address.getAddressLine(0) ?: buildString {
                append(address.featureName ?: "")
                if (!address.locality.isNullOrEmpty()) append(", ${address.locality}")
                if (!address.countryName.isNullOrEmpty()) append(", ${address.countryName}")
            }

        PlacesItem(
            id = index,
            placeName = placeName,
            placeAddressName = placeAddress,
            address = address,
        )
    }

@OptIn(FlowPreview::class)
fun ChooseLocationDialogFragment.setUpSearchAction() {
    // Setup recycler view
    placesAdapter = PlacesAdapter()
    placesAdapter.setOnItemClickListener { address ->
        isUserClickLocationItem = true
        getPlaceDetails(address)
        binding.searchInput.clearFocus()
        binding.searchInput.hideKeyboard()
    }

    binding.placesRecyclerView.adapter = placesAdapter

    // Setup search functionality
    binding.searchButton.setOnClickListener {
        performSearch()
    }

    binding.searchInput
        .textChangesFlow()
        // .debounce(300)
        .onEach {
            performSearch()
        }.launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChooseLocationDialogFragment.performSearch() {
    if (isUserClickLocationItem) {
        Timber.d("performSearch: isUserClickLocationItem")
        isUserClickLocationItem = false
        return
    }

    val query =
        binding.searchInput.text
            .toString()
            .trim()
    if (query.isEmpty()) {
        Timber.d("performSearch: query is empty")
        return
    }

    viewLifecycleOwner.lifecycleScope.launch {
        try {
            val geocoder = Geocoder(requireContext())

            // Use getFromLocationName to get up to 5 nearest locations
            val addresses =
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // For Android 13+
                    val addressList = mutableListOf<Address>()
                    geocoder.getFromLocationName(query, 5) { addresses ->
                        addressList.addAll(addresses)
                    }
                    // Wait a moment for the callback to complete
                    delay(500)
                    addressList
                } else {
                    // For Android 12 and below
                    geocoder.getFromLocationName(query, 5) ?: emptyList()
                }

            Timber.d("performSearch: addresses: $addresses")
            val places = createPlacesList(addresses)
            binding.placesRecyclerView.isVisible = places.isNotEmpty()
            placesAdapter.submitList(places)
        } catch (e: Exception) {
            Timber.e(e)
            displayToast(
                getString(
                    R.string.place_search_failed,
                    e.message ?: getString(R.string.unknown),
                ),
            )
        }
    }
}

fun ChooseLocationDialogFragment.getPlaceDetails(address: Address) {
    binding.placesRecyclerView.isVisible = false

    try {
        val latLng = LatLng(address.latitude, address.longitude)
        Timber.d("getPlaceDetails: latLng: $latLng")
        updateSelectedLocation(latLng)

        // Set the full address in the search input
        val addressText =
            address.getAddressLine(0)
                ?: "${address.featureName ?: ""}, ${address.locality ?: ""}, ${address.countryName ?: ""}"
        binding.searchInput.setText(addressText)
    } catch (e: Exception) {
        Timber.e(e)
        displayToast(
            getString(
                R.string.place_details_failed,
                e.message ?: getString(R.string.unknown),
            ),
        )
    }
}

fun ChooseLocationDialogFragment.showAds() {
    if (AdsConstant.listConfigAds["change-location"]?.type == AdDef.ADS_TYPE_ADMOB.NATIVE) {
        showLoadedNative(
            spaceNameConfig = "change-location",
            spaceName = "change-location_native",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
            onAdsClick = {},
        )
    }
    if (AdsConstant.listConfigAds["change-location"]?.type == AdDef.ADS_TYPE_ADMOB.BANNER_ADAPTIVE) {
        showLoadedBannerAdaptive(
            spaceNameConfig = "change-location",
            spaceName = "change-location_adaptive",
            ratioView = "360:70",
            layoutToAttachAds = binding.adViewGroup,
            layoutContainAds = binding.layoutAds,
        )
    }
    safePreloadAds(
        listSpaceNameConfig = listOf("change-location-back", "change-location-done"),
        spaceNameAds = "camera-1ID_interstitial",
    )
}
