package co.cameralocation.framework.presentation.editgridphoto

import android.Manifest
import android.view.View
import androidx.activity.OnBackPressedCallback
import dagger.hilt.android.AndroidEntryPoint
import co.cameralocation.databinding.FragmentEditGridPhotoBinding
import co.cameralocation.framework.presentation.common.BaseFragment
import timber.log.Timber

@AndroidEntryPoint
class EditGridPhotoFragment : BaseFragment<FragmentEditGridPhotoBinding, EditGridPhotoViewModel>(
    FragmentEditGridPhotoBinding::inflate,
    EditGridPhotoViewModel::class.java
) {

    val permissions = listOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    override fun init(view: View) {
        setUpCurrentLocation()
        setupBackButton()
        setupGridData()
        setupButtons()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observeUiEvent()
        observeGridData()
    }

    companion object {
        const val TAG = "EditGridPhotoFragment"
    }
}
