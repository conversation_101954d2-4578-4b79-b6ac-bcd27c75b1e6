package co.cameralocation.framework.presentation.editlocation

import android.Manifest
import android.view.View
import androidx.fragment.app.setFragmentResultListener
import co.cameralocation.databinding.FragmentEditLocationNewBinding
import co.cameralocation.framework.presentation.common.BaseFragment
import co.cameralocation.framework.presentation.editlocation.adapter.LayoutAdapter
import co.cameralocation.framework.presentation.editlocation.adapter.RecentLocationAdapter
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo
import co.cameralocation.util.Constant
import com.google.android.gms.maps.GoogleMap
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

@AndroidEntryPoint
class EditLocationNewFragment :
    BaseFragment<FragmentEditLocationNewBinding, EditLocationNewViewModel>(
        FragmentEditLocationNewBinding::inflate,
        EditLocationNewViewModel::class.java,
    ) {
    var googleMap: GoogleMap? = null

    var userLocationInfo: LocationInfo? = null

    lateinit var recentLocationAdapter: RecentLocationAdapter

    lateinit var editLayoutAdapter: LayoutAdapter

    val permissions =
        listOf(
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
        )

    var isUpdateAfterEditData = false
    var isApplyDone = false

    override fun init(view: View) {
        commonViewModel.requestSavePreviousItem()
        setFragmentResultListener(Constant.KEY_TRIGGER_RELOAD_LOCATION_DATA) { requestKey, bundle ->
            Timber.d("setFragmentResultListener: KEY_TRIGGER_RELOAD_LOCATION_DATA")
            viewModel.loadRecentLocations()
        }
        initMap()
        refreshButtonEvent()
        setupBackButton()
        setupRecyclerViewRecentLocation()
        setupRecyclerViewEditLayout()
        onClickTabChangeLocationEvent()
        onClickTabChangeLayoutEvent()
        onClickAddNewLocationEvent()
        onClickDoneEvent()
        showAds()
    }

    override fun subscribeObserver(view: View) {
        observerCurrentLocationChanged()
        observeRecentLocationsChanged()
        observeLoadingDialogState()
        observeChangeTabEvent()
        observeLayoutItemSelectedChanged()
    }

    companion object {
        const val TAG = "EditLocationNewFragment"
    }
}
