plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.dagger.hilt.android)
    alias(libs.plugins.google.devtools.ksp)
    alias(libs.plugins.gms.google.services)
    alias(libs.plugins.google.firebase.crashlytics)
    id("kotlin-parcelize")
    id("kotlin-kapt")
    id("kotlin-android")
    id("com.google.android.libraries.mapsplatform.secrets-gradle-plugin")
}

android {
    namespace = "co.cameralocation"
    compileSdk = libs.versions.compileSdkVersion.get().toInt()
    defaultConfig {
        applicationId = "co.cameralocation"
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()
        versionCode = 1
        versionName = "dev_1.0.1_r11"

        setProperty("archivesBaseName", "cameragps_${versionName}")


        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        javaCompileOptions {
            annotationProcessorOptions {
                argument("room.schemaLocation", "$projectDir/schemas")
                argument("room.incremental", "true")
                argument("room.expandProjection", "true")
            }
        }
    }

    buildTypes {
        debug {
            buildConfigField("String", "BASE_URL", "\"http://cms.piontech.site:9123/stores/\"")
            buildConfigField("String", "BASE_SATELLITE_URL", "\"https://satellite.datlt2.site/\"")
        }
        release {
            buildConfigField("String", "BASE_URL", "\"http://cms.piontech.site:9123/stores/\"")
            buildConfigField("String", "BASE_SATELLITE_URL", "\"https://satellite.datlt2.site/\"")
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro",
            )
            ndk {
                abiFilters += listOf("x86", "x86_64", "armeabi-v7a", "arm64-v8a")
            }
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    buildFeatures {
        viewBinding = true
        dataBinding = true
        buildConfig = true
    }

    ksp {
        arg("room.schemaLocation", "$projectDir/schemas")
        arg("room.incremental", "true")
        arg("room.expandProjection", "true")
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    secrets {
        // To add your Maps API key to this project:
        // 1. If the secrets.properties file does not exist, create it in the same folder as the local.properties file.
        // 2. Add this line, where YOUR_API_KEY is your API key:
        //        MAPS_API_KEY=YOUR_API_KEY
        propertiesFileName = "secrets.properties"

        // A properties file containing default secret values. This file can be
        // checked in version control.
        defaultPropertiesFileName = "local.defaults.properties"
    }
}

dependencies {

    implementation(libs.kotlin.stdlib)
    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.appcompat.resources)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.legacy.support.v4)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.aar", "*.jar"))))
    implementation(project(":commonRes"))

    // Link lib: https://github.com/guolindev/PermissionX
    implementation(project(":permissionx"))

    implementation(project(":toggle"))

    implementation(project(":LibIAP"))
    implementation(project(":LibAds"))

    // gpu image
    implementation(libs.gpuimage)

    // crop image
    implementation(libs.android.image.cropper)

    // color picker
    implementation("com.github.skydoves:colorpickerview:2.3.0")

    // implementation(libs.ffmpeg.kit.min)
    implementation(libs.android.tiffbitmapfactory)

    // FFmpeg
    implementation(files("libs/ffmpeg-kit-full-5.1.LTS.aar"))
    implementation(files("libs/smart-exception-java9-0.2.1.jar"))
    implementation("com.arthenica:smart-exception-java:0.2.1")

    // Indicator
    implementation("me.relex:circleindicator:2.1.6")

    // Camera View
    implementation(project(":cameraview"))

    // ZXing cho QR code
    implementation(libs.zxing.core)
    implementation(libs.zxing.android.embedded)

    // Google Maps và Location
    implementation(libs.play.services.maps)
    implementation(libs.play.services.location)
    implementation(libs.maps.ktx)
    implementation(libs.maps.utils.ktx)

    // Crash recovery
    implementation(libs.lib.recovery)

    // Card View
    implementation(libs.androidx.cardview)

    // Recyclerview
    implementation(libs.androidx.recyclerview)

    // ViewModel & LiveData
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.lifecycle.livedata.ktx)

    // Hilt
    implementation(libs.hilt.android)
    ksp(libs.hilt.compiler)

    implementation(libs.androidx.navigation.fragment.ktx)

    // Coroutines
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    // Room
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // Glide
    api(libs.glide)
    annotationProcessor(libs.glide.compiler)

    // Firebase
    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.crashlytics.ktx)
    implementation(libs.firebase.analytics.ktx)
    implementation(libs.firebase.config.ktx)

    // Viewpager2
    implementation(libs.androidx.viewpager2)

    // Nav component
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)

    // Retrofit2
    implementation(libs.retrofit)
    implementation(libs.converter.gson)

    // Okhttp3
    implementation(platform(libs.okhttp.bom))
    implementation(libs.okhttp3.okhttp)
    implementation(libs.okhttp3.logging.interceptor)

    // Material dialog
    implementation(libs.core)
    implementation(libs.lifecycle)
    implementation(libs.bottomsheets)

    // Auto dimen
    implementation(libs.autodimension)

    // Rounded Image View
    implementation(libs.roundedimageview)

    // Timber
    implementation(libs.timber)

    // Lottie
    implementation(libs.lottie)

    // Chucker
    debugImplementation(libs.chucker.library)
    releaseImplementation(libs.chucker.library.no.op)

    // Exoplayer / Media3
    implementation(libs.androidx.media3.ui)
    implementation(libs.androidx.media3.common)
    implementation(libs.androidx.media3.exoplayer)


    implementation("com.github.zladnrms:RoundableLayout:1.1.4")

    implementation(libs.androidyoutubeplayer.core)
}
