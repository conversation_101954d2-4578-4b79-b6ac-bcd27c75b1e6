apply plugin: 'com.android.library'

ext {
    bintrayRepo = 'maven'
    bintrayName = 'Toggle'

    publishedGroupId = 'com.github.angads25'
    libraryName = 'Toggle'
    artifact = 'toggle'

    libraryDescription = 'Android Library for Custom Switches.'

    siteUrl = 'https://github.com/angads25/android-toggle'
    gitUrl = 'https://github.com/angads25/android-toggle.git'

    libraryVersion = '1.1.0'

    developerId = 'angads25'
    developerName = '<PERSON><PERSON>'
    developerEmail = '<EMAIL>'

    licenseName = 'The Apache Software License, Version 2.0'
    licenseUrl = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
    allLicenses = ["Apache-2.0"]
}

android {
    namespace = "com.github.angads25.toggle"
    compileSdkVersion libs.versions.compileSdkVersion.get().toInteger()

    defaultConfig {
        minSdkVersion libs.versions.minSdkVersion.get().toInteger()
        targetSdkVersion libs.versions.targetSdkVersion.get().toInteger()
        versionCode 1
        versionName "1.1.0"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

//apply from: 'https://raw.githubusercontent.com/angads25/JCenter/master/installv1.gradle'
//apply from: 'https://raw.githubusercontent.com/angads25/JCenter/master/bintrayv1.gradle'